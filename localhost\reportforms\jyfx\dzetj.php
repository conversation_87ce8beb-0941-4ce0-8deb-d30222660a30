<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>到账额统计 - 公司数据总览系统</title>
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">到账额统计</h2>
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" id="query-btn">提交</button>
                    </div>
                </form>
            </div>
            
            <div class="row">
                <?
                $sql="SELECT COALESCE(SUM(`ysje`), 0) as hj FROM `tuqoa_htsf` WHERE `sfjs`='是' and `sksj`>='$startDate' and `sksj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $hj=$row["hj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月到账额</h5>
                            <h2 class="card-text">¥<?=$hj?>万</h2>
                        </div>
                    </div>
                </div>
                <?
                $sql="SELECT SUM(COALESCE(yjje, 0)) AS 预计金额总和, SUM(COALESCE(ysje, 0)) AS 已收金额总和, CASE WHEN SUM(COALESCE(yjje, 0)) = 0 THEN 0 ELSE ROUND(SUM(COALESCE(ysje, 0)) / SUM(COALESCE(yjje, 0)) * 100, 2) END AS hkbfb FROM tuqoa_htsf where `sksj`>='$startDate' and `sksj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $hkbfb=$row["hkbfb"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月回款率</h5>
                            <h2 class="card-text"><?=$hkbfb?>%</h2>
                        </div>
                    </div>
                </div>
                <?
                $sql="SELECT COALESCE(SUM(`yjje`), 0) as hj FROM `tuqoa_htsf` WHERE `sfjs`<>'是' and `sksj`>='$startDate' and `sksj`<='$endDate' ";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $hj=$row["hj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">待回款金额</h5>
                            <h2 class="card-text">¥<?=$hj?>万</h2>
                        </div>
                    </div>
                </div>
                <?
                $sql="SELECT SUM(ysje) AS ndhj FROM tuqoa_htsf WHERE YEAR(sksj) = YEAR(CURRENT_DATE) and `sfjs`='是'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $ndhj=$row["ndhj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">年度累计到账</h5>
                            <h2 class="card-text">¥<?=$ndhj?>万</h2>
                        </div>
                    </div>
                </div>
            </div>
            <?php
            // 修复WITH语法兼容性问题 - 使用循环方式生成最近6个月数据
            $months = [];
            $yjjeData = [];
            $ysjeData = [];

            // 生成最近6个月的数据
            for ($i = 5; $i >= 0; $i--) {
                $month = date('Y-m', strtotime("-$i month"));
                $monthDisplay = date('m', strtotime("-$i month")); // 只取月份部分

                // 查询该月的数据
                $sql = "SELECT
                            COALESCE(SUM(yjje), 0) AS yjhj,
                            COALESCE(SUM(ysje), 0) AS yshj
                        FROM tuqoa_htsf
                        WHERE DATE_FORMAT(kpsj, '%Y-%m') = '$month'
                        AND (status IS NULL OR status <> 5)";

                $result = mysqli_query($link, $sql);
                if ($result && $row = mysqli_fetch_assoc($result)) {
                    $months[] = $monthDisplay;
                    $yjjeData[] = (float)$row["yjhj"];
                    $ysjeData[] = (float)$row["yshj"];
                } else {
                    // 如果查询失败，添加默认值
                    $months[] = $monthDisplay;
                    $yjjeData[] = 0;
                    $ysjeData[] = 0;
                }
            }
            ?>
            <div class="row mt-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">到账额趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
                // 查询项目回款分布数据
                $sql="SELECT p.gcname, COALESCE(SUM(h.ysje), 0) as project_payment
                      FROM tuqoa_gcproject p
                      LEFT JOIN tuqoa_htsf h ON p.id = h.projectid
                      AND h.sfjs='是' AND h.sksj>='$startDate' AND h.sksj<='$endDate'
                      WHERE p.xmzt in ('新开工项目','在建项目','完工未结算')
                      GROUP BY p.id, p.gcname
                      HAVING project_payment > 0
                      ORDER BY project_payment DESC
                      LIMIT 8";
                $result = mysqli_query($link, $sql);
                $projectData = ['labels' => [], 'data' => []];
                while ($row = mysqli_fetch_assoc($result)) {
                    $projectData['labels'][] = $row["gcname"];
                    $projectData['data'][] = (float)$row["project_payment"];
                }
                ?>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目回款分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">到账明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>项目名称</th>
                                            <th>预计金额</th>
                                            <th>到账金额</th>
                                            <th>差额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?
                                        $sql="SELECT * FROM `tuqoa_htsf` WHERE `sfjs`='是' and `sksj`>='$startDate' and `sksj`<='$endDate'";
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            $hj=$row["hj"];
                                        ?>
                                        <tr>
                                            <td><?=$row["htmc"]?></td>
                                            <td>¥<?=$row["yjje"]?>万</td>
                                            <td>¥<?=$row["ysje"]?>万</td>
                                            <td>¥<?=$row["yjje"]-$row["ysje"]?>万</td>
                                        </tr>
                                       <?}?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询日期范围:', startDate, '至', endDate);
                // 模拟数据刷新
                //alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
            // 到账额趋势图表
            const trendCtx = document.getElementById('paymentTrendChart').getContext('2d');
            const labels = <?php echo json_encode($months); ?>; // 自动转为 JS 数组格式
            new Chart(trendCtx, {
            type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '预计金额',
                        data: <?php echo json_encode($yjjeData); ?>,
                        borderColor: '#e53935',
                        tension: 0.1
                    }, {
                        label: '已收金额',
                        data: <?php echo json_encode($ysjeData); ?>,
                        borderColor: '#00FF00',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        },
                        y1: {
                            beginAtZero: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '回款率（%）'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });

            // 项目回款分布图表
            const typeCtx = document.getElementById('paymentTypeChart').getContext('2d');
            const projectData = <?php echo json_encode($projectData); ?>;

            if (projectData.labels.length > 0 && projectData.data.length > 0) {
                new Chart(typeCtx, {
                    type: 'pie',
                    data: {
                        labels: projectData.labels,
                        datasets: [{
                            data: projectData.data,
                            backgroundColor: [
                                '#1e88e5', '#e53935', '#43a047', '#ffb300', '#9c27b0',
                                '#00bcd4', '#ff5722', '#795548', '#607d8b', '#3f51b5'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    padding: 10,
                                    generateLabels: function(chart) {
                                        const data = chart.data;
                                        if (data.labels.length && data.datasets.length) {
                                            return data.labels.map((label, i) => {
                                                const value = data.datasets[0].data[i];
                                                return {
                                                    text: label.length > 8 ? label.substring(0, 8) + '...' : label,
                                                    fillStyle: data.datasets[0].backgroundColor[i],
                                                    strokeStyle: data.datasets[0].backgroundColor[i],
                                                    lineWidth: 0,
                                                    index: i
                                                };
                                            });
                                        }
                                        return [];
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.parsed || 0;
                                        return label + ': ¥' + value + '万';
                                    }
                                }
                            }
                        }
                    }
                });
            } else {
                // 如果没有数据，显示提示信息
                typeCtx.font = '14px Arial';
                typeCtx.fillStyle = '#999';
                typeCtx.textAlign = 'center';
                typeCtx.fillText('暂无项目回款数据', typeCtx.canvas.width/2, typeCtx.canvas.height/2);
            }
        }
    </script>
</body>
</html> 