<!DOCTYPE html>
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经营动态 - 公司数据总览系统</title>
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">经营动态</h2>
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" id="query-btn">提交</button>
                    </div>
                </form>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">近期合同信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>合同编号</th>
                                            <th>客户名称</th>
                                            <th>合同金额</th>
                                            <th>签订日期</th>
                                        </tr>
                                    </thead>
                                    <?php
                                    $sql="SELECT * FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate' order by qdsj desc";
                                    $result = mysqli_query($link, $sql);
                                    while ($row = mysqli_fetch_assoc($result)) {
                                        $zrs=$row["zrs"];
                                    ?>
                                    <tbody>
                                        <tr>
                                            <td><?php echo $row["htbh"];?></td>
                                            <td><a href="/task.php?a=p&num=htgl&mid=<?=$row["id"]?>" target="_blank"><?php echo $row["htmc"];?></a></td>
                                            <td><?php echo $row["fwf"];?></td>
                                            <td><?php echo $row["qdsj"];?></td>
                                        </tr>
                                    </tbody>
                                    <?php
                                    }
                                    ?>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT COUNT(*) zrs FROM `tuqoa_userinfo` where state<>5";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $zrs=$row["zrs"];
                }
                $sql="SELECT count(*) byrz FROM `tuqoa_userinfo` WHERE `workdate`>='$startDate' and `workdate`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $byrz=$row["byrz"];
                }
                
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">人员信息统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6>总人数</h6>
                                            <h3><?php echo $zrs; ?></h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6>本月新增</h6>
                                            <h3><?php echo $byrz; ?></h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <h6>部门分布</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar" role="progressbar" style="width: 30%">技术部 30%</div>
                                </div>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 25%">市场部 25%</div>
                                </div>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 20%">运营部 20%</div>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: 25%">其他 25%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php
mysqli_close($link);
?>
</body>
</html> 