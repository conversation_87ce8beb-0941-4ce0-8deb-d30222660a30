<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资质证书动态 - 公司数据总览系统</title>
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">资质证书动态</h2>
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" id="query-btn">提交</button>
                    </div>
                </form>
            </div>
            
            <div class="row">
                <?php
                $sql="SELECT count(*) zszs FROM `tuqoa_userzheng`";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $zszs=$row["zszs"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">总证书数</h5>
                            <h2 class="card-text"><?php echo $zszs; ?>个</h2>
                            
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT count(*) byxz FROM `tuqoa_userzheng` WHERE `sdt`>='$startDate' and `sdt`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $byxz=$row["byxz"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月新增证书</h5>
                            <h2 class="card-text"><?php echo $byxz; ?>个</h2>
                            
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT count(*) bydq FROM `tuqoa_userzheng` WHERE `edt`>='$startDate' and `edt`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $bydq=$row["bydq"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月到期证书</h5>
                            <h2 class="card-text"><?php echo $bydq; ?>个</h2>
                            
                        </div>
                    </div>
                </div>
                <?php
                $sql="SELECT count(*) zfzs FROM `tuqoa_userzheng` where sfzf='是'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $zfzs=$row["zfzs"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">作废证书</h5>
                            <h2 class="card-text"><?php echo $zfzs; ?>个</h2>
                            
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <?
                $sql="WITH months AS ( SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 5 MONTH), '%Y-%m') AS month UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 4 MONTH), '%Y-%m') UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 3 MONTH), '%Y-%m') UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 2 MONTH), '%Y-%m') UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m') UNION ALL SELECT DATE_FORMAT(CURDATE(), '%Y-%m') ) SELECT m.month, IFNULL(COUNT(DATE_FORMAT(u.sdt, '%Y-%m')), 0) AS acquired_count, IFNULL(COUNT(DATE_FORMAT(u.edt, '%Y-%m')), 0) AS expired_count FROM months m LEFT JOIN tuqoa_userzheng u ON DATE_FORMAT(u.sdt, '%Y-%m') = m.month OR DATE_FORMAT(u.edt, '%Y-%m') = m.month GROUP BY m.month ORDER BY m.month ASC";
                $result = mysqli_query($link, $sql);
                $months = [];
                $acquired_count = [];
                $expired_count = [];
                while ($row = mysqli_fetch_assoc($result)) {
                    $months[] = substr($row["month"], -2); // 只取月份部分
                    $acquired_count[] = (float)$row["acquired_count"];
                    $expired_count[] = (float)$row["expired_count"];
                }
                ?>
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">证书变动趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="certificateTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
                // 查询证书类型分布数据，移除不存在的sfzf字段
                $sql="SELECT mingc AS lx, COUNT(*) AS sl FROM tuqoa_userzheng WHERE mingc IS NOT NULL AND mingc != '' GROUP BY mingc ORDER BY sl DESC";
                $result = mysqli_query($link, $sql);
                $deptData = ['labels' => [], 'data' => []];

                if ($result && mysqli_num_rows($result) > 0) {
                    while ($row = mysqli_fetch_assoc($result)) {
                        if ($row["lx"] && $row["sl"] > 0) {
                            $deptData['labels'][] = $row["lx"];
                            $deptData['data'][] = (int)$row["sl"];
                        }
                    }
                } else {
                    // 如果查询失败，记录错误信息
                    if (!$result) {
                        error_log("SQL Error in zzzsdt.php: " . mysqli_error($link));
                    }
                }
                ?>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">证书类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="certificateTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">证书到期明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>证书名称</th>
                                            <th>证书类型</th>
                                            <th>持有人</th>
                                            <th>证书编号</th>
                                            <th>到期日期</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <?php
                                    $sql="SELECT * FROM `tuqoa_userzheng` WHERE `edt`>='$startDate' and `edt`<='$endDate' ORDER BY edt ASC";
                                    $result = mysqli_query($link, $sql);

                                    if ($result && mysqli_num_rows($result) > 0) {
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            // 判断证书状态
                                            $today = date('Y-m-d');
                                            $status = '';
                                            $statusClass = '';

                                            if ($row["edt"] && $row["edt"] < $today) {
                                                $status = '已过期';
                                                $statusClass = 'bg-danger';
                                            } elseif ($row["edt"] && $row["edt"] <= date('Y-m-d', strtotime('+30 days'))) {
                                                $status = '即将到期';
                                                $statusClass = 'bg-warning';
                                            } else {
                                                $status = '有效';
                                                $statusClass = 'bg-success';
                                            }
                                    ?>
                                    <tbody>
                                        <tr>
                                            <td><a href="https://mas.nxyhy.cn/task.php?a=p&num=userzheng&mid=<?php echo $row["id"];?>" target="_blank"><?php echo $row["mingc"];?></a></td>
                                            <td><?php echo $row["mingc"];?></td>
                                            <td><?php echo $row["uname"];?></td>
                                            <td><?php echo $row["numc"];?></td>
                                            <td><?php echo $row["edt"];?></td>
                                            <td><span class="badge <?php echo $statusClass;?>"><?php echo $status;?></span></td>
                                        </tr>
                                    </tbody>
                                    <?php
                                        }
                                    } else {
                                        // 如果没有数据，显示提示信息
                                    ?>
                                    <tbody>
                                        <tr>
                                            <td colspan="6" style="text-align: center; color: #999;">暂无证书到期数据</td>
                                        </tr>
                                    </tbody>
                                    <?php
                                    }
                                    ?>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
    mysqli_close($link);
    ?>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            //document.getElementById('start-date').value = formatDate(firstDay);
            //document.getElementById('end-date').value = formatDate(lastDay);
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询日期范围:', startDate, '至', endDate);
                // 模拟数据刷新
                //alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
            // 证书变动趋势图表
            const trendCtx = document.getElementById('certificateTrendChart').getContext('2d');
            const labels = <?php echo json_encode($months); ?>; // 自动转为 JS 数组格式
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '新增',
                        data: <?php echo json_encode($acquired_count); ?>,
                        borderColor: '#1e88e5',
                        tension: 0.1
                    }, {
                        label: '到期',
                        data: <?php echo json_encode($expired_count); ?>,
                        borderColor: '#e53935',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '数量'
                            }
                        }
                    }
                }
            });

            // 证书类型分布图表
            const typeCtx = document.getElementById('certificateTypeChart').getContext('2d');
            const deptData = <?php echo json_encode($deptData); ?>;

            if (deptData.labels.length > 0 && deptData.data.length > 0) {
                new Chart(typeCtx, {
                    type: 'pie',
                    data: {
                        labels: deptData.labels,
                        datasets: [{
                            data: deptData.data,
                            backgroundColor: [
                                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                                '#FF9F40', '#8AC24A', '#607D8B', '#E91E63'
                            ],
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    padding: 10
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.parsed || 0;
                                        return label + ': ' + value + '个';
                                    }
                                }
                            }
                        }
                    }
                });
            } else {
                // 如果没有数据，显示提示信息
                typeCtx.font = '14px Arial';
                typeCtx.fillStyle = '#999';
                typeCtx.textAlign = 'center';
                typeCtx.fillText('暂无证书数据', typeCtx.canvas.width/2, typeCtx.canvas.height/2);
            }
        }
    </script>
</body>
</html> 