<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m');
$lastDayOfMonth = date('Y-m');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月', strtotime($startDate));
        $displayEnd = date('Y年m月', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
    <title>项目费用明细表</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        .header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
            position: sticky;
            top: 0;
            background-color: #f8fafc;
            z-index: 20;
        }
        .header h2 {
            color: #2d3748;
            margin: 0;
        }
        .header p {
            color: #718096;
            margin: 5px 0 0;
            font-size: 14px;
        }
        .table-container {
            width: 100%;
            height: calc(100vh - 180px); /* 设置固定高度，留出空间给页眉页脚 */
            overflow: auto;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            background: white;
            position: relative;
        }
        table {
            width: 100%;
            min-width: 1800px;
            border-collapse: collapse;
            font-size: 14px;
        }
        thead {
            position: sticky;
            top: 0;
            z-index: 15;
        }
        th {
            background-color: #2563eb;
            color: white;
            padding: 8px 12px;
            text-align: center;
            font-weight: 500;
            white-space: nowrap;
            border: 1px solid rgba(255,255,255,0.2);
            position: sticky;
            top: 0;
        }
        .sub-header th {
            background-color: #3b82f6;
            top: 38px; /* 第一行表头高度 */
        }
        .sub-sub-header th {
            background-color: #60a5fa;
            top: 76px; /* 第一行和第二行表头高度之和 */
        }
        td {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            white-space: nowrap;
            color: #4a5568;
            text-align: right;
        }
        td:first-child, td:nth-child(2), td:nth-child(3) {
            text-align: left;
        }
        tbody tr:hover td {
            background-color: #f7fafc;
        }
        tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }
        .footer {
            margin-top: 15px;
            font-size: 12px;
            color: #718096;
            text-align: right;
            position: sticky;
            bottom: 0;
            background-color: #f8fafc;
            padding: 10px 0;
        }
        .text-left {
            text-align: left;
        }
        .text-center {
            text-align: center;
        }
        .highlight {
            font-weight: 500;
        }
    </style>
    <script language="JavaScript" type="text/javascript">
            var idTmr;
            function getExplorer() {
              var explorer = window.navigator.userAgent ;
              //ie
              if (explorer.indexOf("MSIE") >= 0) {
                return 'ie';
              }
              //firefox
              else if (explorer.indexOf("Firefox") >= 0) {
                return 'Firefox';
              }
              //Chrome
              else if(explorer.indexOf("Chrome") >= 0){
                return 'Chrome';
              }
              //Opera
              else if(explorer.indexOf("Opera") >= 0){
                return 'Opera';
              }
              //Safari
              else if(explorer.indexOf("Safari") >= 0){
                return 'Safari';
              }
            }
            function method5(tableid) {
              if(getExplorer()=='ie')
              {
                var curTbl = document.getElementById(tableid);
                var oXL = new ActiveXObject("Excel.Application");
                var oWB = oXL.Workbooks.Add();
                var xlsheet = oWB.Worksheets(1);
                var sel = document.body.createTextRange();
                sel.moveToElementText(curTbl);
                sel.select();
                sel.execCommand("Copy");
                xlsheet.Paste();
                oXL.Visible = true;
                try {
                  var fname = oXL.Application.GetSaveAsFilename("Excel.xls", "Excel Spreadsheets (*.xls), *.xls");
                } catch (e) {
                  print("Nested catch caught " + e);
                } finally {
                  oWB.SaveAs(fname);
                  oWB.Close(savechanges = false);
                  oXL.Quit();
                  oXL = null;
                  idTmr = window.setInterval("Cleanup();", 1);
                }
              }
              else
              {
                tableToExcel(tableid)
              }
            }
            function Cleanup() {
              window.clearInterval(idTmr);
              CollectGarbage();
            }
            var tableToExcel = (function() {
              var uri = 'data:application/vnd.ms-excel;base64,',
                  template = '<html><head><meta charset="UTF-8"></head><body><table>{table}</table></body></html>',
                  base64 = function(s) { return window.btoa(unescape(encodeURIComponent(s))) },
                  format = function(s, c) {
                    return s.replace(/{(\w+)}/g,
                        function(m, p) { return c[p]; }) }
              return function(table, name) {
                if (!table.nodeType) table = document.getElementById(table)
                var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML}
                window.location.href = uri + base64(format(template, ctx))
              }
            })()
          </script>
</head>
<body>
    <div class="header">
        <h2>项目费用明细表</h2>
    </div>
    <div class="date-range-container">
        <form method="post" action="">
            <div class="form-group">
                <label for="start-date">开始日期:</label>
                <input type="month" id="start-date" name="start-date" 
                       value="<?php echo isset($_POST['start-date']) ? htmlspecialchars($_POST['start-date']) : date('Y-m'); ?>">
                <label for="end-date">结束日期:</label>
                <input type="month" id="end-date" name="end-date" 
                       value="<?php echo isset($_POST['end-date']) ? htmlspecialchars($_POST['end-date']) : date('Y-m'); ?>">
                <button type="submit" id="query-btn">提交</button>
                <button type="button" onclick="method5('tableExcel')">导出Excel</button>
            </div>
        </form>
    </div>
    <div class="table-container">
        <table id="tableExcel" >
            <thead>
                <tr>
                    <th rowspan="3" class="text-center">序号</th>
                    <th rowspan="3" class="text-center">项目名称</th>
                    <th colspan="15">直接费</th>
                    <th colspan="2" rowspan="2">间接费</th>
                    <th rowspan="3">利润（%）</th>
                    <th rowspan="3">税金（8.2%）</th>
                </tr>
                <tr class="sub-header">
                    <th colspan="7">人工费</th>
                    <th colspan="8">其他直接费</th>
                </tr>
                <tr class="sub-sub-header">
                    <th>实发工资</th>
                    <th>公积金（单位+个人）</th>
                    <th>社医保（单位+个人）</th>
                    <th>伙食费</th>
                    <th>采暖费</th>
                    <th>福利费</th>
                    <th>工会经费（工资*2%）</th>
                    
                    <th>购买行政用品</th>
                    <th>办公费</th>
                    <th>折旧费</th>
                    <th>低值易耗品摊销</th>
                    <th>差旅费</th>
                    <th>其他费用</th>
                    <th>中标服务费</th>
                    <th>业务招待费</th>
                    <th>企业管理费</th>
                    
                    <th>经营业务费</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $sql="SELECT * FROM `tuqoa_gcproject` WHERE xmzt in ('新开工项目','在建项目','完工未结算') order by id desc";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                ?>
                <tr>
                    <td class="text-center highlight"><?php echo $row["id"]?></td>
                    <td class="text-left"><?php echo $row["gcname"]?></td>
                    <?php
                    $实发工资合计=0;
                    $应发工资合计=0;
                    $公积金合计=0;
                    $伙食费合计=0;
                    $采暖费合计=0;
                    $sql1="SELECT * FROM `tuqoa_rydp` WHERE `drxmid`=".$row["id"]." and `sfqz`='全职' and `state`='在职'";
                    $result1 = mysqli_query($link, $sql1);
                    while ($row1 = mysqli_fetch_assoc($result1)) {
                        //print_r($row1["dpryxm"]);
                        $sql2="SELECT *  FROM `tuqoa_hrsalary` WHERE `uname`='".$row1["dpryxm"]."' and  `month`>='$startDate' and `month`<='$endDate'";
                        $result2 = mysqli_query($link, $sql2);
                        while ($row2 = mysqli_fetch_assoc($result2)) {
                            $实发工资合计+=$row2["sfgz"];
                            $应发工资合计+=$row2["yfgz"];
                            $公积金合计+=$row2["zfgjj"]*2;
                            $伙食费合计+=$row2["foodbt"];
                            $采暖费合计+=$row2["cnf"];
                        }
                    }
                    $date = new DateTime($endDate);
                    $lastDayOfMonth = $date->format('Y-m-t');
                    /////////////////////////////////////////////////项目上缴社保费用明细///////////////////////////////////////////
                    $养老合计=0;
                    $医疗报销合计=0;
                    $sql1="SELECT ifnull(sum(ylxj),0) ylhj,ifnull(sum(yiliaobxxj),0) yiliaobxxjhj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and  `ys`>='$startDate.-01' and `ys`<='$lastDayOfMonth'";
                    //$sql1="SELECT ifnull(sum(sjje),0) as hj FROM `tuqoa_xmsjbxmx` WHERE `projectid`=".$row["id"]." and `ys` like '$selectedMonth%'";
                    $result1 = mysqli_query($link, $sql1);
                    while ($row1 = mysqli_fetch_assoc($result1)) {
                        $养老合计=$row1["ylhj"];
                        $医疗报销合计=$row1["yiliaobxxjhj"];
                    }
                    /////////////////////////////////////////////////企业上缴社保管理费等//////////////////////////////////////////////////////////
                    //$sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and `sbrq` like '$selectedMonth%'";
                    $福利费合计=0;
                    $购买行政用品=0;
                    $办公费=0;
                    $折旧费=0;
                    $低值易耗品摊销=0;
                    $差旅费=0;
                    $其他费用=0;
                    $中标服务费=0;
                    $业务招待费=0;
                    $企业管理费=0;
                    $经营业务费=0;
                    $利润=0;
                    $税金=0;
                    $sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE `projectid`=".$row["id"]." and  `sbrq`>='$startDate.-01' and `sbrq`<='$lastDayOfMonth'";
                    $result1 = mysqli_query($link, $sql1);
                    while ($row1 = mysqli_fetch_assoc($result1)) {
                        $福利费合计+=$row1["flf"];
                        $购买行政用品+=$row1["gmxzyp"];
                        $办公费+=$row1["bgf"];
                        $折旧费+=$row1["zzjf"];
                        $低值易耗品摊销+=$row1["dzyhptx"];
                        $差旅费+=$row1["clf"];
                        $其他费用+=$row1["qtfy"];
                        $中标服务费+=$row1["zbfwf"];
                        $业务招待费+=$row1["ywzdf"];
                        $企业管理费+=$row1["qyglf"];
                        $经营业务费+=$row1["jyywf"];
                        $利润+=$row1["lr"];
                        $税金+=$row1["sj"];
                    }
                    ?>
                    <td><?php echo $实发工资合计?></td>
                    <td><?php echo $公积金合计?></td>
                    <td><?php echo $养老合计+$医疗报销合计?></td>
                    <td><?php echo $伙食费合计?></td>
                    <td><?php echo $采暖费合计?></td>
                    <td><?php echo $福利费合计?></td>
                    <td><?php echo round($应发工资合计*0.02,2)?></td>
                    
                    <td><?php echo $购买行政用品?></td>
                    <td><?php echo $办公费?></td>
                    <td><?php echo $折旧费?></td>
                    <td><?php echo $低值易耗品摊销?></td>
                    <td><?php echo $差旅费?></td>
                    <td><?php echo $其他费用?></td>
                    <td><?php echo $中标服务费?></td>
                    <td><?php echo $业务招待费?></td>
                    <td><?php echo $企业管理费?></td>
                    <td><?php echo $经营业务费?></td>
                    
                    
                    <td><?php echo $利润?></td>
                    <td><?php echo $税金?></td>
                </tr>
                <?php
                }
                ?>
            </tbody>
        </table>
    </div>
</body>
</html>