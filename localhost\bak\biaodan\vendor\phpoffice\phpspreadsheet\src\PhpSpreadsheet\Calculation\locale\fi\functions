############################################################
##
## PhpSpreadsheet - function name translations
##
## Suomi (Finnish)
##
############################################################


##
## Kuutiofunktiot (Cube Functions)
##
CUBEKPIMEMBER = KUUTIOKPIJÄSEN
CUBEMEMBER = KUUTIONJÄSEN
CUBEMEMBERPROPERTY = KU<PERSON><PERSON><PERSON>ÄSENENOMINAISUUS
CUBERANKEDMEMBER = KUUTIONLUOKITELTUJÄSEN
CUBESET = KUUTIOJOUKKO
CUBESETCOUNT = KUUTIOJOUKKOJENMÄÄRÄ
CUBEVALUE = KUUTIONARVO

##
## Tietokantafunktiot (Database Functions)
##
DAVERAGE = TKESKIARVO
DCOUNT = TLASKE
DCOUNTA = TLASKEA
DGET = TNOUDA
DMAX = TMAKS
DMIN = TMIN
DPRODUCT = TTULO
DSTDEV = TKESKIHAJONTA
DSTDEVP = TKESKIHAJONTAP
DSUM = TSUMMA
DVAR = TVARIANSSI
DVARP = TVARIANSSIP

##
## Päivämäärä- ja aikafunktiot (Date & Time Functions)
##
DATE = PÄIVÄYS
DATEDIF = PVMERO
DATESTRING = PVMMERKKIJONO
DATEVALUE = PÄIVÄYSARVO
DAY = PÄIVÄ
DAYS = PÄIVÄT
DAYS360 = PÄIVÄT360
EDATE = PÄIVÄ.KUUKAUSI
EOMONTH = KUUKAUSI.LOPPU
HOUR = TUNNIT
ISOWEEKNUM = VIIKKO.ISO.NRO
MINUTE = MINUUTIT
MONTH = KUUKAUSI
NETWORKDAYS = TYÖPÄIVÄT
NETWORKDAYS.INTL = TYÖPÄIVÄT.KANSVÄL
NOW = NYT
SECOND = SEKUNNIT
THAIDAYOFWEEK = THAI.VIIKONPÄIVÄ
THAIMONTHOFYEAR = THAI.KUUKAUSI
THAIYEAR = THAI.VUOSI
TIME = AIKA
TIMEVALUE = AIKA_ARVO
TODAY = TÄMÄ.PÄIVÄ
WEEKDAY = VIIKONPÄIVÄ
WEEKNUM = VIIKKO.NRO
WORKDAY = TYÖPÄIVÄ
WORKDAY.INTL = TYÖPÄIVÄ.KANSVÄL
YEAR = VUOSI
YEARFRAC = VUOSI.OSA

##
## Tekniset funktiot (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BINDES
BIN2HEX = BINHEKSA
BIN2OCT = BINOKT
BITAND = BITTI.JA
BITLSHIFT = BITTI.SIIRTO.V
BITOR = BITTI.TAI
BITRSHIFT = BITTI.SIIRTO.O
BITXOR = BITTI.EHDOTON.TAI
COMPLEX = KOMPLEKSI
CONVERT = MUUNNA
DEC2BIN = DESBIN
DEC2HEX = DESHEKSA
DEC2OCT = DESOKT
DELTA = SAMA.ARVO
ERF = VIRHEFUNKTIO
ERF.PRECISE = VIRHEFUNKTIO.TARKKA
ERFC = VIRHEFUNKTIO.KOMPLEMENTTI
ERFC.PRECISE = VIRHEFUNKTIO.KOMPLEMENTTI.TARKKA
GESTEP = RAJA
HEX2BIN = HEKSABIN
HEX2DEC = HEKSADES
HEX2OCT = HEKSAOKT
IMABS = KOMPLEKSI.ABS
IMAGINARY = KOMPLEKSI.IMAG
IMARGUMENT = KOMPLEKSI.ARG
IMCONJUGATE = KOMPLEKSI.KONJ
IMCOS = KOMPLEKSI.COS
IMCOSH = IMCOSH
IMCOT = KOMPLEKSI.COT
IMCSC = KOMPLEKSI.KOSEK
IMCSCH = KOMPLEKSI.KOSEKH
IMDIV = KOMPLEKSI.OSAM
IMEXP = KOMPLEKSI.EKSP
IMLN = KOMPLEKSI.LN
IMLOG10 = KOMPLEKSI.LOG10
IMLOG2 = KOMPLEKSI.LOG2
IMPOWER = KOMPLEKSI.POT
IMPRODUCT = KOMPLEKSI.TULO
IMREAL = KOMPLEKSI.REAALI
IMSEC = KOMPLEKSI.SEK
IMSECH = KOMPLEKSI.SEKH
IMSIN = KOMPLEKSI.SIN
IMSINH = KOMPLEKSI.SINH
IMSQRT = KOMPLEKSI.NELIÖJ
IMSUB = KOMPLEKSI.EROTUS
IMSUM = KOMPLEKSI.SUM
IMTAN = KOMPLEKSI.TAN
OCT2BIN = OKTBIN
OCT2DEC = OKTDES
OCT2HEX = OKTHEKSA

##
## Rahoitusfunktiot (Financial Functions)
##
ACCRINT = KERTYNYT.KORKO
ACCRINTM = KERTYNYT.KORKO.LOPUSSA
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = KORKOPÄIVÄT.ALUSTA
COUPDAYS = KORKOPÄIVÄT
COUPDAYSNC = KORKOPÄIVÄT.SEURAAVA
COUPNCD = KORKOPÄIVÄ.SEURAAVA
COUPNUM = KORKOPÄIVÄ.JAKSOT
COUPPCD = KORKOPÄIVÄ.EDELLINEN
CUMIPMT = MAKSETTU.KORKO
CUMPRINC = MAKSETTU.LYHENNYS
DB = DB
DDB = DDB
DISC = DISKONTTOKORKO
DOLLARDE = VALUUTTA.DES
DOLLARFR = VALUUTTA.MURTO
DURATION = KESTO
EFFECT = KORKO.EFEKT
FV = TULEVA.ARVO
FVSCHEDULE = TULEVA.ARVO.ERIKORKO
INTRATE = KORKO.ARVOPAPERI
IPMT = IPMT
IRR = SISÄINEN.KORKO
ISPMT = ISPMT
MDURATION = KESTO.MUUNN
MIRR = MSISÄINEN
NOMINAL = KORKO.VUOSI
NPER = NJAKSO
NPV = NNA
ODDFPRICE = PARITON.ENS.NIMELLISARVO
ODDFYIELD = PARITON.ENS.TUOTTO
ODDLPRICE = PARITON.VIIM.NIMELLISARVO
ODDLYIELD = PARITON.VIIM.TUOTTO
PDURATION = KESTO.JAKSO
PMT = MAKSU
PPMT = PPMT
PRICE = HINTA
PRICEDISC = HINTA.DISK
PRICEMAT = HINTA.LUNASTUS
PV = NA
RATE = KORKO
RECEIVED = SAATU.HINTA
RRI = TOT.ROI
SLN = STP
SYD = VUOSIPOISTO
TBILLEQ = OBLIG.TUOTTOPROS
TBILLPRICE = OBLIG.HINTA
TBILLYIELD = OBLIG.TUOTTO
VDB = VDB
XIRR = SISÄINEN.KORKO.JAKSOTON
XNPV = NNA.JAKSOTON
YIELD = TUOTTO
YIELDDISC = TUOTTO.DISK
YIELDMAT = TUOTTO.ERÄP

##
## Tietofunktiot (Information Functions)
##
CELL = SOLU
ERROR.TYPE = VIRHEEN.LAJI
INFO = KUVAUS
ISBLANK = ONTYHJÄ
ISERR = ONVIRH
ISERROR = ONVIRHE
ISEVEN = ONPARILLINEN
ISFORMULA = ONKAAVA
ISLOGICAL = ONTOTUUS
ISNA = ONPUUTTUU
ISNONTEXT = ONEI_TEKSTI
ISNUMBER = ONLUKU
ISODD = ONPARITON
ISREF = ONVIITT
ISTEXT = ONTEKSTI
N = N
NA = PUUTTUU
SHEET = TAULUKKO
SHEETS = TAULUKOT
TYPE = TYYPPI

##
## Loogiset funktiot (Logical Functions)
##
AND = JA
FALSE = EPÄTOSI
IF = JOS
IFERROR = JOSVIRHE
IFNA = JOSPUUTTUU
IFS = JOSS
NOT = EI
OR = TAI
SWITCH = MUUTA
TRUE = TOSI
XOR = EHDOTON.TAI

##
## Haku- ja viitefunktiot (Lookup & Reference Functions)
##
ADDRESS = OSOITE
AREAS = ALUEET
CHOOSE = VALITSE.INDEKSI
COLUMN = SARAKE
COLUMNS = SARAKKEET
FORMULATEXT = KAAVA.TEKSTI
GETPIVOTDATA = NOUDA.PIVOT.TIEDOT
HLOOKUP = VHAKU
HYPERLINK = HYPERLINKKI
INDEX = INDEKSI
INDIRECT = EPÄSUORA
LOOKUP = HAKU
MATCH = VASTINE
OFFSET = SIIRTYMÄ
ROW = RIVI
ROWS = RIVIT
RTD = RTD
TRANSPOSE = TRANSPONOI
VLOOKUP = PHAKU

##
## Matemaattiset ja trigonometriset funktiot (Math & Trig Functions)
##
ABS = ITSEISARVO
ACOS = ACOS
ACOSH = ACOSH
ACOT = ACOT
ACOTH = ACOTH
AGGREGATE = KOOSTE
ARABIC = ARABIA
ASIN = ASIN
ASINH = ASINH
ATAN = ATAN
ATAN2 = ATAN2
ATANH = ATANH
BASE = PERUS
CEILING.MATH = PYÖRISTÄ.KERR.YLÖS.MATEMAATTINEN
CEILING.PRECISE = PYÖRISTÄ.KERR.YLÖS.TARKKA
COMBIN = KOMBINAATIO
COMBINA = KOMBINAATIOA
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = KOSEK
CSCH = KOSEKH
DECIMAL = DESIMAALI
DEGREES = ASTEET
ECMA.CEILING = ECMA.PYÖRISTÄ.KERR.YLÖS
EVEN = PARILLINEN
EXP = EKSPONENTTI
FACT = KERTOMA
FACTDOUBLE = KERTOMA.OSA
FLOOR.MATH = PYÖRISTÄ.KERR.ALAS.MATEMAATTINEN
FLOOR.PRECISE = PYÖRISTÄ.KERR.ALAS.TARKKA
GCD = SUURIN.YHT.TEKIJÄ
INT = KOKONAISLUKU
ISO.CEILING = ISO.PYÖRISTÄ.KERR.YLÖS
LCM = PIENIN.YHT.JAETTAVA
LN = LUONNLOG
LOG = LOG
LOG10 = LOG10
MDETERM = MDETERM
MINVERSE = MKÄÄNTEINEN
MMULT = MKERRO
MOD = JAKOJ
MROUND = PYÖRISTÄ.KERR
MULTINOMIAL = MULTINOMI
MUNIT = YKSIKKÖM
ODD = PARITON
PI = PII
POWER = POTENSSI
PRODUCT = TULO
QUOTIENT = OSAMÄÄRÄ
RADIANS = RADIAANIT
RAND = SATUNNAISLUKU
RANDBETWEEN = SATUNNAISLUKU.VÄLILTÄ
ROMAN = ROMAN
ROUND = PYÖRISTÄ
ROUNDBAHTDOWN = PYÖRISTÄ.BAHT.ALAS
ROUNDBAHTUP = PYÖRISTÄ.BAHT.YLÖS
ROUNDDOWN = PYÖRISTÄ.DES.ALAS
ROUNDUP = PYÖRISTÄ.DES.YLÖS
SEC = SEK
SECH = SEKH
SERIESSUM = SARJA.SUMMA
SIGN = ETUMERKKI
SIN = SIN
SINH = SINH
SQRT = NELIÖJUURI
SQRTPI = NELIÖJUURI.PII
SUBTOTAL = VÄLISUMMA
SUM = SUMMA
SUMIF = SUMMA.JOS
SUMIFS = SUMMA.JOS.JOUKKO
SUMPRODUCT = TULOJEN.SUMMA
SUMSQ = NELIÖSUMMA
SUMX2MY2 = NELIÖSUMMIEN.EROTUS
SUMX2PY2 = NELIÖSUMMIEN.SUMMA
SUMXMY2 = EROTUSTEN.NELIÖSUMMA
TAN = TAN
TANH = TANH
TRUNC = KATKAISE

##
## Tilastolliset funktiot (Statistical Functions)
##
AVEDEV = KESKIPOIKKEAMA
AVERAGE = KESKIARVO
AVERAGEA = KESKIARVOA
AVERAGEIF = KESKIARVO.JOS
AVERAGEIFS = KESKIARVO.JOS.JOUKKO
BETA.DIST = BEETA.JAKAUMA
BETA.INV = BEETA.KÄÄNT
BINOM.DIST = BINOMI.JAKAUMA
BINOM.DIST.RANGE = BINOMI.JAKAUMA.ALUE
BINOM.INV = BINOMIJAKAUMA.KÄÄNT
CHISQ.DIST = CHINELIÖ.JAKAUMA
CHISQ.DIST.RT = CHINELIÖ.JAKAUMA.OH
CHISQ.INV = CHINELIÖ.KÄÄNT
CHISQ.INV.RT = CHINELIÖ.KÄÄNT.OH
CHISQ.TEST = CHINELIÖ.TESTI
CONFIDENCE.NORM = LUOTTAMUSVÄLI.NORM
CONFIDENCE.T = LUOTTAMUSVÄLI.T
CORREL = KORRELAATIO
COUNT = LASKE
COUNTA = LASKE.A
COUNTBLANK = LASKE.TYHJÄT
COUNTIF = LASKE.JOS
COUNTIFS = LASKE.JOS.JOUKKO
COVARIANCE.P = KOVARIANSSI.P
COVARIANCE.S = KOVARIANSSI.S
DEVSQ = OIKAISTU.NELIÖSUMMA
EXPON.DIST = EKSPONENTIAALI.JAKAUMA
F.DIST = F.JAKAUMA
F.DIST.RT = F.JAKAUMA.OH
F.INV = F.KÄÄNT
F.INV.RT = F.KÄÄNT.OH
F.TEST = F.TESTI
FISHER = FISHER
FISHERINV = FISHER.KÄÄNT
FORECAST.ETS = ENNUSTE.ETS
FORECAST.ETS.CONFINT = ENNUSTE.ETS.CONFINT
FORECAST.ETS.SEASONALITY = ENNUSTE.ETS.KAUSIVAIHTELU
FORECAST.ETS.STAT = ENNUSTE.ETS.STAT
FORECAST.LINEAR = ENNUSTE.LINEAARINEN
FREQUENCY = TAAJUUS
GAMMA = GAMMA
GAMMA.DIST = GAMMA.JAKAUMA
GAMMA.INV = GAMMA.JAKAUMA.KÄÄNT
GAMMALN = GAMMALN
GAMMALN.PRECISE = GAMMALN.TARKKA
GAUSS = GAUSS
GEOMEAN = KESKIARVO.GEOM
GROWTH = KASVU
HARMEAN = KESKIARVO.HARM
HYPGEOM.DIST = HYPERGEOM_JAKAUMA
INTERCEPT = LEIKKAUSPISTE
KURT = KURT
LARGE = SUURI
LINEST = LINREGR
LOGEST = LOGREGR
LOGNORM.DIST = LOGNORM_JAKAUMA
LOGNORM.INV = LOGNORM.KÄÄNT
MAX = MAKS
MAXA = MAKSA
MAXIFS = MAKS.JOS
MEDIAN = MEDIAANI
MIN = MIN
MINA = MINA
MINIFS = MIN.JOS
MODE.MULT = MOODI.USEA
MODE.SNGL = MOODI.YKSI
NEGBINOM.DIST = BINOMI.JAKAUMA.NEG
NORM.DIST = NORMAALI.JAKAUMA
NORM.INV = NORMAALI.JAKAUMA.KÄÄNT
NORM.S.DIST = NORM_JAKAUMA.NORMIT
NORM.S.INV = NORM_JAKAUMA.KÄÄNT
PEARSON = PEARSON
PERCENTILE.EXC = PROSENTTIPISTE.ULK
PERCENTILE.INC = PROSENTTIPISTE.SIS
PERCENTRANK.EXC = PROSENTTIJÄRJESTYS.ULK
PERCENTRANK.INC = PROSENTTIJÄRJESTYS.SIS
PERMUT = PERMUTAATIO
PERMUTATIONA = PERMUTAATIOA
PHI = FII
POISSON.DIST = POISSON.JAKAUMA
PROB = TODENNÄKÖISYYS
QUARTILE.EXC = NELJÄNNES.ULK
QUARTILE.INC = NELJÄNNES.SIS
RANK.AVG = ARVON.MUKAAN.KESKIARVO
RANK.EQ = ARVON.MUKAAN.TASAN
RSQ = PEARSON.NELIÖ
SKEW = JAKAUMAN.VINOUS
SKEW.P = JAKAUMAN.VINOUS.POP
SLOPE = KULMAKERROIN
SMALL = PIENI
STANDARDIZE = NORMITA
STDEV.P = KESKIHAJONTA.P
STDEV.S = KESKIHAJONTA.S
STDEVA = KESKIHAJONTAA
STDEVPA = KESKIHAJONTAPA
STEYX = KESKIVIRHE
T.DIST = T.JAKAUMA
T.DIST.2T = T.JAKAUMA.2S
T.DIST.RT = T.JAKAUMA.OH
T.INV = T.KÄÄNT
T.INV.2T = T.KÄÄNT.2S
T.TEST = T.TESTI
TREND = SUUNTAUS
TRIMMEAN = KESKIARVO.TASATTU
VAR.P = VAR.P
VAR.S = VAR.S
VARA = VARA
VARPA = VARPA
WEIBULL.DIST = WEIBULL.JAKAUMA
Z.TEST = Z.TESTI

##
## Tekstifunktiot (Text Functions)
##
BAHTTEXT = BAHTTEKSTI
CHAR = MERKKI
CLEAN = SIIVOA
CODE = KOODI
CONCAT = YHDISTÄ
DOLLAR = VALUUTTA
EXACT = VERTAA
FIND = ETSI
FIXED = KIINTEÄ
ISTHAIDIGIT = ON.THAI.NUMERO
LEFT = VASEN
LEN = PITUUS
LOWER = PIENET
MID = POIMI.TEKSTI
NUMBERSTRING = NROMERKKIJONO
NUMBERVALUE = NROARVO
PHONETIC = FONEETTINEN
PROPER = ERISNIMI
REPLACE = KORVAA
REPT = TOISTA
RIGHT = OIKEA
SEARCH = KÄY.LÄPI
SUBSTITUTE = VAIHDA
T = T
TEXT = TEKSTI
TEXTJOIN = TEKSTI.YHDISTÄ
THAIDIGIT = THAI.NUMERO
THAINUMSOUND = THAI.LUKU.ÄÄNI
THAINUMSTRING = THAI.LUKU.MERKKIJONO
THAISTRINGLENGTH = THAI.MERKKIJONON.PITUUS
TRIM = POISTA.VÄLIT
UNICHAR = UNICODEMERKKI
UNICODE = UNICODE
UPPER = ISOT
VALUE = ARVO

##
## Verkkofunktiot (Web Functions)
##
ENCODEURL = URLKOODAUS
FILTERXML = SUODATA.XML
WEBSERVICE = VERKKOPALVELU

##
## Yhteensopivuusfunktiot (Compatibility Functions)
##
BETADIST = BEETAJAKAUMA
BETAINV = BEETAJAKAUMA.KÄÄNT
BINOMDIST = BINOMIJAKAUMA
CEILING = PYÖRISTÄ.KERR.YLÖS
CHIDIST = CHIJAKAUMA
CHIINV = CHIJAKAUMA.KÄÄNT
CHITEST = CHITESTI
CONCATENATE = KETJUTA
CONFIDENCE = LUOTTAMUSVÄLI
COVAR = KOVARIANSSI
CRITBINOM = BINOMIJAKAUMA.KRIT
EXPONDIST = EKSPONENTIAALIJAKAUMA
FDIST = FJAKAUMA
FINV = FJAKAUMA.KÄÄNT
FLOOR = PYÖRISTÄ.KERR.ALAS
FORECAST = ENNUSTE
FTEST = FTESTI
GAMMADIST = GAMMAJAKAUMA
GAMMAINV = GAMMAJAKAUMA.KÄÄNT
HYPGEOMDIST = HYPERGEOM.JAKAUMA
LOGINV = LOGNORM.JAKAUMA.KÄÄNT
LOGNORMDIST = LOGNORM.JAKAUMA
MODE = MOODI
NEGBINOMDIST = BINOMIJAKAUMA.NEG
NORMDIST = NORM.JAKAUMA
NORMINV = NORM.JAKAUMA.KÄÄNT
NORMSDIST = NORM.JAKAUMA.NORMIT
NORMSINV = NORM.JAKAUMA.NORMIT.KÄÄNT
PERCENTILE = PROSENTTIPISTE
PERCENTRANK = PROSENTTIJÄRJESTYS
POISSON = POISSON
QUARTILE = NELJÄNNES
RANK = ARVON.MUKAAN
STDEV = KESKIHAJONTA
STDEVP = KESKIHAJONTAP
TDIST = TJAKAUMA
TINV = TJAKAUMA.KÄÄNT
TTEST = TTESTI
VAR = VAR
VARP = VARP
WEIBULL = WEIBULL
ZTEST = ZTESTI
