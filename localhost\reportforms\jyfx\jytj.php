<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经营统计 - 公司数据总览系统</title>
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">经营统计</h2>
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" id="query-btn">提交</button>
                    </div>
                </form>
            </div>
            
            <div class="row">
                <?php
                //计算服务费合计
                $sql="SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $fwfhj=$row["fwfhj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">合同额合计</h5>
                            <h2 class="card-text">¥<?php echo $fwfhj?>万</h2>
                       </div>
                    </div>
                </div>
                <?php
                //计算预计收费合计
                $sql="SELECT sum(yjje) as yjjehj FROM `tuqoa_htsf` WHERE `yjsj`>='$startDate' and `yjsj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $yjjehj=$row["yjjehj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">预计营收额</h5>
                            <h2 class="card-text">¥<?php echo $yjjehj?>万</h2>
                       </div>
                    </div>
                </div>
                <?php
                //计算回款合计
                $sql="SELECT sum(ysje) as ysjehj FROM `tuqoa_htsf` WHERE `sksj`>='$startDate' and `sksj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $ysjehj=$row["ysjehj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月回款额</h5>
                            <h2 class="card-text">¥<?php echo $ysjehj?>万</h2>
                         </div>
                    </div>
                </div>
                <?php
                $lrhj=$yjjehj-$ysjehj;
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">差额</h5>
                            <h2 class="card-text">¥<?php echo $lrhj?>万</h2>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <?
                //统计最近6个月的合同签订服务费额度
                $sql="SELECT months.month AS month, IFNULL(SUM(t.fwf), 0) AS total_service_fee FROM ( SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 5 MONTH), '%Y-%m') AS month UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 4 MONTH), '%Y-%m') UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 3 MONTH), '%Y-%m') UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 2 MONTH), '%Y-%m') UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH), '%Y-%m') UNION ALL SELECT DATE_FORMAT(CURRENT_DATE(), '%Y-%m') ) AS months LEFT JOIN tuqoa_htgl t ON DATE_FORMAT(t.qdsj, '%Y-%m') = months.month GROUP BY months.month ORDER BY months.month";
                $result = mysqli_query($link, $sql);
                $total_service_fee = [];
                while ($row = mysqli_fetch_assoc($result)) {
                    $total_service_fee[] = (float)$row["total_service_fee"];
                }
                //统计最近6个月预计收费和已收费数据 - 使用兼容性更好的SQL
                $months = [];
                $yjjeData = [];
                $ysjeData = [];

                // 生成最近6个月的数据
                for ($i = 5; $i >= 0; $i--) {
                    $month = date('Y-m', strtotime("-$i month"));
                    $monthDisplay = date('m', strtotime("-$i month")); // 只取月份部分

                    // 查询该月的数据
                    $sql = "SELECT
                                IFNULL(SUM(yjje), 0) AS total_yjje,
                                IFNULL(SUM(ysje), 0) AS total_ysje
                            FROM tuqoa_htsf
                            WHERE DATE_FORMAT(yjsj, '%Y-%m') = '$month'";

                    $result = mysqli_query($link, $sql);
                    if ($result && $row = mysqli_fetch_assoc($result)) {
                        $months[] = $monthDisplay;
                        $yjjeData[] = (float)$row["total_yjje"];
                        $ysjeData[] = (float)$row["total_ysje"];
                    } else {
                        // 如果查询失败，添加默认值
                        $months[] = $monthDisplay;
                        $yjjeData[] = 0;
                        $ysjeData[] = 0;
                    }
                }
                ?>
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">经营指标趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="businessTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <?
                $sql="SELECT htfl AS 'lx', SUM(fwf) AS 'ze' FROM tuqoa_htgl GROUP BY htfl ORDER BY SUM(fwf) DESC";
                $result = mysqli_query($link, $sql);
                $deptData = ['labels' => [], 'data' => []];
                while ($row = mysqli_fetch_assoc($result)) {
                    $deptData['labels'][] = $row["lx"];
                    $deptData['data'][] = $row["ze"];
                }
                ?>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">业务类型分布</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="businessTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">经营指标明细</h5>
                        </div>
                        <!--
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>指标名称</th>
                                            <th>本月值</th>
                                            <th>上月值</th>
                                            <th>环比变化</th>
                                            <th>同比变化</th>
                                            <th>年度累计</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>合同额</td>
                                            <td>¥1,500万</td>
                                            <td>¥1,300万</td>
                                            <td class="text-success">+15.4%</td>
                                            <td class="text-success">+25.0%</td>
                                            <td>¥8,500万</td>
                                        </tr>
                                        <tr>
                                            <td>营收额</td>
                                            <td>¥1,200万</td>
                                            <td>¥1,090万</td>
                                            <td class="text-success">+10.1%</td>
                                            <td class="text-success">+20.0%</td>
                                            <td>¥6,800万</td>
                                        </tr>
                                        <tr>
                                            <td>利润额</td>
                                            <td>¥300万</td>
                                            <td>¥250万</td>
                                            <td class="text-success">+20.0%</td>
                                            <td class="text-success">+30.0%</td>
                                            <td>¥1,700万</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>-->
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php
mysqli_close($link);
?>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询日期范围:', startDate, '至', endDate);
                // 模拟数据刷新
                alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 经营指标趋势图表
        const trendCtx = document.getElementById('businessTrendChart').getContext('2d');
        const labels = <?php echo json_encode($months); ?>; // 自动转为 JS 数组格式
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '合同额',
                    data: <?php echo json_encode($total_service_fee); ?>,
                    borderColor: '#1e88e5',
                    tension: 0.1
                }, {
                    label: '预计收款',
                    data: <?php echo json_encode($yjjeData); ?>,
                    borderColor: '#e53935',
                    tension: 0.1
                }, {
                    label: '实际收款',
                    data: <?php echo json_encode($ysjeData); ?>,
                    borderColor: '#00FF00',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '金额（万元）'
                        }
                    }
                }
            }
        });

        // 业务类型分布图表
        const typeCtx = document.getElementById('businessTypeChart').getContext('2d');
        const deptData = <?php echo json_encode($deptData); ?>;
        new Chart(typeCtx, {
            type: 'pie',
            data: {
                labels: deptData.labels,
                datasets: [{
                    data: deptData.data,
                    backgroundColor: [
                                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                                '#FF9F40', '#8AC24A', '#607D8B', '#E91E63'
                            ],
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    </script>
</body>
</html> 