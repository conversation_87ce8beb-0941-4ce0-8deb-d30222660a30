############################################################
##
## PhpSpreadsheet - function name translations
##
## Deutsch (German)
##
############################################################


##
## Cubefunktionen (Cube Functions)
##
CUBEKPIMEMBER = CUBEKPIELEMENT
CUBEMEMBER = CUBEELEMENT
CUBEMEMBERPROPERTY = CUBEELEMENTEIGENSCHAFT
CUBERANKEDMEMBER = CUBERANGELEMENT
CUBESET = CUBEMENGE
CUBESETCOUNT = CUBEMENGENANZAHL
CUBEVALUE = CUBEWERT

##
## Datenbankfunktionen (Database Functions)
##
DAVERAGE = DBMITTELWERT
DCOUNT = DBANZAHL
DCOUNTA = DBANZAHL2
DGET = DBAUSZUG
DMAX = DBMAX
DMIN = DBMIN
DPRODUCT = DBPRODUKT
DSTDEV = DBSTDABW
DSTDEVP = DBSTDABWN
DSUM = DBSUMME
DVAR = DBVARIANZ
DVARP = DBVARIANZEN

##
## Datums- und Uhrzeitfunktionen (Date & Time Functions)
##
DATE = DATUM
DATEVALUE = DATWERT
DAY = TAG
DAYS = TAGE
DAYS360 = TAGE360
EDATE = EDATUM
EOMONTH = MONATSENDE
HOUR = STUNDE
ISOWEEKNUM = ISOKALENDERWOCHE
MINUTE = MINUTE
MONTH = MONAT
NETWORKDAYS = NETTOARBEITSTAGE
NETWORKDAYS.INTL = NETTOARBEITSTAGE.INTL
NOW = JETZT
SECOND = SEKUNDE
THAIDAYOFWEEK = THAIWOCHENTAG
THAIMONTHOFYEAR = THAIMONATDESJAHRES
THAIYEAR = THAIJAHR
TIME = ZEIT
TIMEVALUE = ZEITWERT
TODAY = HEUTE
WEEKDAY = WOCHENTAG
WEEKNUM = KALENDERWOCHE
WORKDAY = ARBEITSTAG
WORKDAY.INTL = ARBEITSTAG.INTL
YEAR = JAHR
YEARFRAC = BRTEILJAHRE

##
## Technische Funktionen (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BININDEZ
BIN2HEX = BININHEX
BIN2OCT = BININOKT
BITAND = BITUND
BITLSHIFT = BITLVERSCHIEB
BITOR = BITODER
BITRSHIFT = BITRVERSCHIEB
BITXOR = BITXODER
COMPLEX = KOMPLEXE
CONVERT = UMWANDELN
DEC2BIN = DEZINBIN
DEC2HEX = DEZINHEX
DEC2OCT = DEZINOKT
DELTA = DELTA
ERF = GAUSSFEHLER
ERF.PRECISE = GAUSSF.GENAU
ERFC = GAUSSFKOMPL
ERFC.PRECISE = GAUSSFKOMPL.GENAU
GESTEP = GGANZZAHL
HEX2BIN = HEXINBIN
HEX2DEC = HEXINDEZ
HEX2OCT = HEXINOKT
IMABS = IMABS
IMAGINARY = IMAGINÄRTEIL
IMARGUMENT = IMARGUMENT
IMCONJUGATE = IMKONJUGIERTE
IMCOS = IMCOS
IMCOSH = IMCOSHYP
IMCOT = IMCOT
IMCSC = IMCOSEC
IMCSCH = IMCOSECHYP
IMDIV = IMDIV
IMEXP = IMEXP
IMLN = IMLN
IMLOG10 = IMLOG10
IMLOG2 = IMLOG2
IMPOWER = IMAPOTENZ
IMPRODUCT = IMPRODUKT
IMREAL = IMREALTEIL
IMSEC = IMSEC
IMSECH = IMSECHYP
IMSIN = IMSIN
IMSINH = IMSINHYP
IMSQRT = IMWURZEL
IMSUB = IMSUB
IMSUM = IMSUMME
IMTAN = IMTAN
OCT2BIN = OKTINBIN
OCT2DEC = OKTINDEZ
OCT2HEX = OKTINHEX

##
## Finanzmathematische Funktionen (Financial Functions)
##
ACCRINT = AUFGELZINS
ACCRINTM = AUFGELZINSF
AMORDEGRC = AMORDEGRK
AMORLINC = AMORLINEARK
COUPDAYBS = ZINSTERMTAGVA
COUPDAYS = ZINSTERMTAGE
COUPDAYSNC = ZINSTERMTAGNZ
COUPNCD = ZINSTERMNZ
COUPNUM = ZINSTERMZAHL
COUPPCD = ZINSTERMVZ
CUMIPMT = KUMZINSZ
CUMPRINC = KUMKAPITAL
DB = GDA2
DDB = GDA
DISC = DISAGIO
DOLLARDE = NOTIERUNGDEZ
DOLLARFR = NOTIERUNGBRU
DURATION = DURATION
EFFECT = EFFEKTIV
FV = ZW
FVSCHEDULE = ZW2
INTRATE = ZINSSATZ
IPMT = ZINSZ
IRR = IKV
ISPMT = ISPMT
MDURATION = MDURATION
MIRR = QIKV
NOMINAL = NOMINAL
NPER = ZZR
NPV = NBW
ODDFPRICE = UNREGER.KURS
ODDFYIELD = UNREGER.REND
ODDLPRICE = UNREGLE.KURS
ODDLYIELD = UNREGLE.REND
PDURATION = PDURATION
PMT = RMZ
PPMT = KAPZ
PRICE = KURS
PRICEDISC = KURSDISAGIO
PRICEMAT = KURSFÄLLIG
PV = BW
RATE = ZINS
RECEIVED = AUSZAHLUNG
RRI = ZSATZINVEST
SLN = LIA
SYD = DIA
TBILLEQ = TBILLÄQUIV
TBILLPRICE = TBILLKURS
TBILLYIELD = TBILLRENDITE
VDB = VDB
XIRR = XINTZINSFUSS
XNPV = XKAPITALWERT
YIELD = RENDITE
YIELDDISC = RENDITEDIS
YIELDMAT = RENDITEFÄLL

##
## Informationsfunktionen (Information Functions)
##
CELL = ZELLE
ERROR.TYPE = FEHLER.TYP
INFO = INFO
ISBLANK = ISTLEER
ISERR = ISTFEHL
ISERROR = ISTFEHLER
ISEVEN = ISTGERADE
ISFORMULA = ISTFORMEL
ISLOGICAL = ISTLOG
ISNA = ISTNV
ISNONTEXT = ISTKTEXT
ISNUMBER = ISTZAHL
ISODD = ISTUNGERADE
ISREF = ISTBEZUG
ISTEXT = ISTTEXT
N = N
NA = NV
SHEET = BLATT
SHEETS = BLÄTTER
TYPE = TYP

##
## Logische Funktionen (Logical Functions)
##
AND = UND
FALSE = FALSCH
IF = WENN
IFERROR = WENNFEHLER
IFNA = WENNNV
IFS = WENNS
NOT = NICHT
OR = ODER
SWITCH = ERSTERWERT
TRUE = WAHR
XOR = XODER

##
## Nachschlage- und Verweisfunktionen (Lookup & Reference Functions)
##
ADDRESS = ADRESSE
AREAS = BEREICHE
CHOOSE = WAHL
COLUMN = SPALTE
COLUMNS = SPALTEN
FORMULATEXT = FORMELTEXT
GETPIVOTDATA = PIVOTDATENZUORDNEN
HLOOKUP = WVERWEIS
HYPERLINK = HYPERLINK
INDEX = INDEX
INDIRECT = INDIREKT
LOOKUP = VERWEIS
MATCH = VERGLEICH
OFFSET = BEREICH.VERSCHIEBEN
ROW = ZEILE
ROWS = ZEILEN
RTD = RTD
TRANSPOSE = MTRANS
VLOOKUP = SVERWEIS

##
## Mathematische und trigonometrische Funktionen (Math & Trig Functions)
##
ABS = ABS
ACOS = ARCCOS
ACOSH = ARCCOSHYP
ACOT = ARCCOT
ACOTH = ARCCOTHYP
AGGREGATE = AGGREGAT
ARABIC = ARABISCH
ASIN = ARCSIN
ASINH = ARCSINHYP
ATAN = ARCTAN
ATAN2 = ARCTAN2
ATANH = ARCTANHYP
BASE = BASIS
CEILING.MATH = OBERGRENZE.MATHEMATIK
CEILING.PRECISE = OBERGRENZE.GENAU
COMBIN = KOMBINATIONEN
COMBINA = KOMBINATIONEN2
COS = COS
COSH = COSHYP
COT = COT
COTH = COTHYP
CSC = COSEC
CSCH = COSECHYP
DECIMAL = DEZIMAL
DEGREES = GRAD
ECMA.CEILING = ECMA.OBERGRENZE
EVEN = GERADE
EXP = EXP
FACT = FAKULTÄT
FACTDOUBLE = ZWEIFAKULTÄT
FLOOR.MATH = UNTERGRENZE.MATHEMATIK
FLOOR.PRECISE = UNTERGRENZE.GENAU
GCD = GGT
INT = GANZZAHL
ISO.CEILING = ISO.OBERGRENZE
LCM = KGV
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = MDET
MINVERSE = MINV
MMULT = MMULT
MOD = REST
MROUND = VRUNDEN
MULTINOMIAL = POLYNOMIAL
MUNIT = MEINHEIT
ODD = UNGERADE
PI = PI
POWER = POTENZ
PRODUCT = PRODUKT
QUOTIENT = QUOTIENT
RADIANS = BOGENMASS
RAND = ZUFALLSZAHL
RANDBETWEEN = ZUFALLSBEREICH
ROMAN = RÖMISCH
ROUND = RUNDEN
ROUNDBAHTDOWN = RUNDBAHTNED
ROUNDBAHTUP = BAHTAUFRUNDEN
ROUNDDOWN = ABRUNDEN
ROUNDUP = AUFRUNDEN
SEC = SEC
SECH = SECHYP
SERIESSUM = POTENZREIHE
SIGN = VORZEICHEN
SIN = SIN
SINH = SINHYP
SQRT = WURZEL
SQRTPI = WURZELPI
SUBTOTAL = TEILERGEBNIS
SUM = SUMME
SUMIF = SUMMEWENN
SUMIFS = SUMMEWENNS
SUMPRODUCT = SUMMENPRODUKT
SUMSQ = QUADRATESUMME
SUMX2MY2 = SUMMEX2MY2
SUMX2PY2 = SUMMEX2PY2
SUMXMY2 = SUMMEXMY2
TAN = TAN
TANH = TANHYP
TRUNC = KÜRZEN

##
## Statistische Funktionen (Statistical Functions)
##
AVEDEV = MITTELABW
AVERAGE = MITTELWERT
AVERAGEA = MITTELWERTA
AVERAGEIF = MITTELWERTWENN
AVERAGEIFS = MITTELWERTWENNS
BETA.DIST = BETA.VERT
BETA.INV = BETA.INV
BINOM.DIST = BINOM.VERT
BINOM.DIST.RANGE = BINOM.VERT.BEREICH
BINOM.INV = BINOM.INV
CHISQ.DIST = CHIQU.VERT
CHISQ.DIST.RT = CHIQU.VERT.RE
CHISQ.INV = CHIQU.INV
CHISQ.INV.RT = CHIQU.INV.RE
CHISQ.TEST = CHIQU.TEST
CONFIDENCE.NORM = KONFIDENZ.NORM
CONFIDENCE.T = KONFIDENZ.T
CORREL = KORREL
COUNT = ANZAHL
COUNTA = ANZAHL2
COUNTBLANK = ANZAHLLEEREZELLEN
COUNTIF = ZÄHLENWENN
COUNTIFS = ZÄHLENWENNS
COVARIANCE.P = KOVARIANZ.P
COVARIANCE.S = KOVARIANZ.S
DEVSQ = SUMQUADABW
EXPON.DIST = EXPON.VERT
F.DIST = F.VERT
F.DIST.RT = F.VERT.RE
F.INV = F.INV
F.INV.RT = F.INV.RE
F.TEST = F.TEST
FISHER = FISHER
FISHERINV = FISHERINV
FORECAST.ETS = PROGNOSE.ETS
FORECAST.ETS.CONFINT = PROGNOSE.ETS.KONFINT
FORECAST.ETS.SEASONALITY = PROGNOSE.ETS.SAISONALITÄT
FORECAST.ETS.STAT = PROGNOSE.ETS.STAT
FORECAST.LINEAR = PROGNOSE.LINEAR
FREQUENCY = HÄUFIGKEIT
GAMMA = GAMMA
GAMMA.DIST = GAMMA.VERT
GAMMA.INV = GAMMA.INV
GAMMALN = GAMMALN
GAMMALN.PRECISE = GAMMALN.GENAU
GAUSS = GAUSS
GEOMEAN = GEOMITTEL
GROWTH = VARIATION
HARMEAN = HARMITTEL
HYPGEOM.DIST = HYPGEOM.VERT
INTERCEPT = ACHSENABSCHNITT
KURT = KURT
LARGE = KGRÖSSTE
LINEST = RGP
LOGEST = RKP
LOGNORM.DIST = LOGNORM.VERT
LOGNORM.INV = LOGNORM.INV
MAX = MAX
MAXA = MAXA
MAXIFS = MAXWENNS
MEDIAN = MEDIAN
MIN = MIN
MINA = MINA
MINIFS = MINWENNS
MODE.MULT = MODUS.VIELF
MODE.SNGL = MODUS.EINF
NEGBINOM.DIST = NEGBINOM.VERT
NORM.DIST = NORM.VERT
NORM.INV = NORM.INV
NORM.S.DIST = NORM.S.VERT
NORM.S.INV = NORM.S.INV
PEARSON = PEARSON
PERCENTILE.EXC = QUANTIL.EXKL
PERCENTILE.INC = QUANTIL.INKL
PERCENTRANK.EXC = QUANTILSRANG.EXKL
PERCENTRANK.INC = QUANTILSRANG.INKL
PERMUT = VARIATIONEN
PERMUTATIONA = VARIATIONEN2
PHI = PHI
POISSON.DIST = POISSON.VERT
PROB = WAHRSCHBEREICH
QUARTILE.EXC = QUARTILE.EXKL
QUARTILE.INC = QUARTILE.INKL
RANK.AVG = RANG.MITTELW
RANK.EQ = RANG.GLEICH
RSQ = BESTIMMTHEITSMASS
SKEW = SCHIEFE
SKEW.P = SCHIEFE.P
SLOPE = STEIGUNG
SMALL = KKLEINSTE
STANDARDIZE = STANDARDISIERUNG
STDEV.P = STABW.N
STDEV.S = STABW.S
STDEVA = STABWA
STDEVPA = STABWNA
STEYX = STFEHLERYX
T.DIST = T.VERT
T.DIST.2T = T.VERT.2S
T.DIST.RT = T.VERT.RE
T.INV = T.INV
T.INV.2T = T.INV.2S
T.TEST = T.TEST
TREND = TREND
TRIMMEAN = GESTUTZTMITTEL
VAR.P = VAR.P
VAR.S = VAR.S
VARA = VARIANZA
VARPA = VARIANZENA
WEIBULL.DIST = WEIBULL.VERT
Z.TEST = G.TEST

##
## Textfunktionen (Text Functions)
##
BAHTTEXT = BAHTTEXT
CHAR = ZEICHEN
CLEAN = SÄUBERN
CODE = CODE
CONCAT = TEXTKETTE
DOLLAR = DM
EXACT = IDENTISCH
FIND = FINDEN
FIXED = FEST
ISTHAIDIGIT = ISTTHAIZAHLENWORT
LEFT = LINKS
LEN = LÄNGE
LOWER = KLEIN
MID = TEIL
NUMBERVALUE = ZAHLENWERT
PROPER = GROSS2
REPLACE = ERSETZEN
REPT = WIEDERHOLEN
RIGHT = RECHTS
SEARCH = SUCHEN
SUBSTITUTE = WECHSELN
T = T
TEXT = TEXT
TEXTJOIN = TEXTVERKETTEN
THAIDIGIT = THAIZAHLENWORT
THAINUMSOUND = THAIZAHLSOUND
THAINUMSTRING = THAILANDSKNUMSTRENG
THAISTRINGLENGTH = THAIZEICHENFOLGENLÄNGE
TRIM = GLÄTTEN
UNICHAR = UNIZEICHEN
UNICODE = UNICODE
UPPER = GROSS
VALUE = WERT

##
## Webfunktionen (Web Functions)
##
ENCODEURL = URLCODIEREN
FILTERXML = XMLFILTERN
WEBSERVICE = WEBDIENST

##
## Kompatibilitätsfunktionen (Compatibility Functions)
##
BETADIST = BETAVERT
BETAINV = BETAINV
BINOMDIST = BINOMVERT
CEILING = OBERGRENZE
CHIDIST = CHIVERT
CHIINV = CHIINV
CHITEST = CHITEST
CONCATENATE = VERKETTEN
CONFIDENCE = KONFIDENZ
COVAR = KOVAR
CRITBINOM = KRITBINOM
EXPONDIST = EXPONVERT
FDIST = FVERT
FINV = FINV
FLOOR = UNTERGRENZE
FORECAST = SCHÄTZER
FTEST = FTEST
GAMMADIST = GAMMAVERT
GAMMAINV = GAMMAINV
HYPGEOMDIST = HYPGEOMVERT
LOGINV = LOGINV
LOGNORMDIST = LOGNORMVERT
MODE = MODALWERT
NEGBINOMDIST = NEGBINOMVERT
NORMDIST = NORMVERT
NORMINV = NORMINV
NORMSDIST = STANDNORMVERT
NORMSINV = STANDNORMINV
PERCENTILE = QUANTIL
PERCENTRANK = QUANTILSRANG
POISSON = POISSON
QUARTILE = QUARTILE
RANK = RANG
STDEV = STABW
STDEVP = STABWN
TDIST = TVERT
TINV = TINV
TTEST = TTEST
VAR = VARIANZ
VARP = VARIANZEN
WEIBULL = WEIBULL
ZTEST = GTEST
