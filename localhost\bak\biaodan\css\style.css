* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    background-color: #f8f9fa;
    padding: 16px;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    padding: 24px;
}

h1 {
    text-align: center;
    margin-bottom: 24px;
    font-size: 24px;
    color: #2c3e50;
    font-weight: 600;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 15px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* 卡片样式 */
.card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 16px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    transform: translateY(-2px);
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-size: 16px;
}

.loading-spinner {
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 状态提示 */
.status {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-success {
    background-color: #28a745;
    color: white;
}

.status-error {
    background-color: #dc3545;
    color: white;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.table th,
.table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.table tr:hover {
    background-color: #f8f9fa;
}

/* 链接样式 */
.link {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.link:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* 错误提示 */
.error {
    color: #dc3545;
    text-align: center;
    padding: 20px;
    background: #f8d7da;
    border-radius: 6px;
    margin: 20px 0;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    padding: 24px;
    max-width: 500px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.modal-close {
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: #343a40;
}

/* 响应式设计 */
@media (max-width: 576px) {
    body {
        padding: 12px;
    }
    
    .container {
        padding: 16px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .modal-content {
        margin: 5% 12px;
    }
} 