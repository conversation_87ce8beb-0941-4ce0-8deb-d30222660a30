<!DOCTYPE html>
<html lang="zh-CN">
<?php
include '../config.php';
$firstDayOfMonth = date('Y-m-01');
$lastDayOfMonth = date('Y-m-t');
$startDate = isset($_POST['start-date']) ? $_POST['start-date'] : $firstDayOfMonth;
$endDate = isset($_POST['end-date']) ? $_POST['end-date'] : $lastDayOfMonth;
// 初始化日期变量
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // 验证日期
    if (strtotime($startDate) > strtotime($endDate)) {
        echo '<div class="result" style="background-color: #fde8e8;">错误：开始日期不能晚于结束日期</div>';
    } else {
        // 格式化日期用于显示
        $displayStart = date('Y年m月d日', strtotime($startDate));
        $displayEnd = date('Y年m月d日', strtotime($endDate));
        $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24) + 1;
        
       
        
        // 添加本月信息
        $currentMonth = date('Y年m月');
        $monthDays = date('t', strtotime($firstDayOfMonth));
       
    }
}
?>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>月度经营数据分析 - 公司数据总览系统</title>
    <link href="styles/bootstrap.min.css" rel="stylesheet">
    <link href="styles//boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">月度经营数据分析</h2>
            
            <!-- 日期区间选择器 -->
            <div class="date-range-container">
                <form method="post" action="">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" name="start-date" 
                               value="<?php echo htmlspecialchars($startDate); ?>">
                        <label for="end-date">结束日期:</label>
                        <input type="date" id="end-date" name="end-date" 
                               value="<?php echo htmlspecialchars($endDate); ?>">
                        <button type="submit" id="query-btn">提交</button>
                    </div>
                </form>
            </div>
            
            <div class="row">
                <?
                //计算服务费合计
                $sql="SELECT sum(fwf) fwfhj FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $fwfhj=$row["fwfhj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月新接合同额</h5>
                            <h2 class="card-text">¥<?=$fwfhj?>万</h2>
                           
                        </div>
                    </div>
                </div>
                <?
                $sql="SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb`  WHERE `sbrq`>='$startDate' and `sbrq`<='$endDate'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $cz=$row["hj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">总产值</h5>
                            <h2 class="card-text"><?=$cz?>万</h2>
                           
                        </div>
                    </div>
                </div>
                <?
                $sql="SELECT count(*) hj FROM `tuqoa_rydp` WHERE `status`=1 and `state`='在职' and drxm<>'工程管理部待岗人员'";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $zgrs=$row["hj"];
                }
                $rjcz = number_format($cz / $zgrs, 4);
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">人均产值</h5>
                            <h2 class="card-text">¥<?=$rjcz?>万</h2>
                            
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">本月成本占比</h5>
                            <h2 class="card-text">65%</h2>
                           
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <?
                $month=[];
                $fwfhj=[];
                $sql="WITH months AS (
                            SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 5 MONTH), '%Y-%m') AS month
                            UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 4 MONTH), '%Y-%m')
                            UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 3 MONTH), '%Y-%m')
                            UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 2 MONTH), '%Y-%m')
                            UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH), '%Y-%m')
                            UNION ALL SELECT DATE_FORMAT(CURRENT_DATE, '%Y-%m')
                        )
                        
                        SELECT 
                            m.month,
                            IFNULL(SUM(t.fwf), 0) AS fwfhj
                        FROM 
                            months m
                        LEFT JOIN 
                            `tuqoa_htgl` t ON DATE_FORMAT(t.qdsj, '%Y-%m') = m.month
                        GROUP BY 
                            m.month
                        ORDER BY 
                            m.month;";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $month[] = substr($row["month"], -2) . "月"; // 添加"月"字更符合中文习惯
                    $fwfhj[] = (float)$row["fwfhj"];
                }
                $ysjehj=[];
                $sql="WITH months AS (
                        SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 5 MONTH), '%Y-%m') AS month
                        UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 4 MONTH), '%Y-%m')
                        UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 3 MONTH), '%Y-%m')
                        UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 2 MONTH), '%Y-%m')
                        UNION ALL SELECT DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 1 MONTH), '%Y-%m')
                        UNION ALL SELECT DATE_FORMAT(CURRENT_DATE, '%Y-%m')
                    )
                    
                    SELECT 
                        m.month,
                        IFNULL(SUM(t.ysje), 0) AS ysjehj
                    FROM 
                        months m
                    LEFT JOIN 
                        `tuqoa_htsf` t ON DATE_FORMAT(t.yjsj, '%Y-%m') = m.month
                    GROUP BY 
                        m.month
                    ORDER BY 
                        m.month;";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $ysjehj[] = (float)$row["ysjehj"];
                }
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">合同额与到账额趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="contractPaymentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <?
                
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">实际成本与预计成本趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="costSalaryChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度经营数据明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>月份</th>
                                            <th>项目总投资</th>
                                            <th>服务费合计</th>
                                            <th>预计收款</th>
                                            <th>到账</th>
                                            <th>差额</th>
                                            <th>月人数</th>
                                            <th>工资汇总</th>
                                            <th>工资占比</th>
                                            <th>成本汇总</th>
                                            <th>成本占比</th>
                                            <th>人均产值</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?
                                        //$sql="SELECT sum(ztz) ztzhj FROM `tuqoa_htgl` WHERE `qdsj`>='$startDate' and `qdsj`<='$endDate'";
                                        //$sql="SELECT DATE_FORMAT(qdsj, '%Y-%m') AS month, SUM(ztz) AS ztzhj,SUM(fwf) AS fwfhj FROM `tuqoa_htgl` WHERE `qdsj` >= '$startDate' AND `qdsj` <= '$endDate' GROUP BY DATE_FORMAT(qdsj, '%Y-%m') ORDER BY month";
                                        $sql="SELECT 
                                                    months.month,
                                                    COALESCE(SUM(t.ztz), 0) AS ztzhj,
                                                    COALESCE(SUM(t.fwf), 0) AS fwfhj 
                                                FROM (
                                                    -- 生成从开始日期到结束日期的所有月份序列
                                                    SELECT DATE_FORMAT(DATE_ADD('$startDate', INTERVAL n MONTH), '%Y-%m') AS month
                                                    FROM (
                                                        -- 生成0到最大月份差的序列
                                                        SELECT a.N + b.N*10 AS n
                                                        FROM 
                                                            (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 
                                                             UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 
                                                             UNION SELECT 8 UNION SELECT 9) a,
                                                            (SELECT 0 AS N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 
                                                             UNION SELECT 4 UNION SELECT 5) b
                                                    ) numbers
                                                    WHERE DATE_ADD('$startDate', INTERVAL n MONTH) <= LAST_DAY('$endDate')
                                                    GROUP BY month
                                                ) months
                                                LEFT JOIN `tuqoa_htgl` t 
                                                    ON months.month = DATE_FORMAT(t.qdsj, '%Y-%m') 
                                                    AND t.qdsj >= '$startDate' 
                                                    AND t.qdsj <= '$endDate'
                                                GROUP BY months.month
                                                ORDER BY months.month";
                                        //print_r($sql."<br>");
                                        $合同签订月份="";
                                        $总投资合计=0;
                                        $服务费合计=0;
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            $合同签订月份=$row["month"];
                                            $总投资合计=$row["ztzhj"];
                                            $服务费合计=$row["fwfhj"];
                                        ?>
                                        <tr>
                                            <td><?=$合同签订月份?></td>
                                            <td><?=$总投资合计?>万</td>
                                            <td><?=$服务费合计?>万</td>
                                            <?
                                            $应回收款=0;
                                            $已收回款=0;
                                            $差额=0;
                                            //$sql1="SELECT ifnull(sum(yjje),0) as  yjjehj FROM `tuqoa_htsf`  WHERE  projectid=".$row["id"]." and `yjsj` like '$selectedMonth%'";
                                            $sql1="SELECT ifnull(sum(yjje),0) as yjjehj,ifnull(sum(ysje),0) as  ysjehj FROM `tuqoa_htsf`  WHERE  `yjsj` like '$合同签订月份%'";
                                            
                                            //print_r($sql1."<br>");
                                            $result1 = mysqli_query($link, $sql1);
                                            while ($row1 = mysqli_fetch_assoc($result1)) {
                                                $应回收款=$row1["yjjehj"];
                                                $已收回款=$row1["ysjehj"];
                                                $差额=$应回收款-$已收回款;
                                            }
                                            ?>
                                            <td>¥<?=$应回收款?>万</td>
                                            <td>¥<?=$已收回款?>万</td>
                                            <td>¥<?=$差额?>万</td>
                                            <?
                                            $月人数=0;
                                            $应发工资合计=0;
                                            $sql1="SELECT count(*) yrs,sum(sfgz) sfgzhj FROM `tuqoa_hrsalary` WHERE  `month`='".$合同签订月份."'";
                                            $result1 = mysqli_query($link, $sql1);
                                            while ($row1 = mysqli_fetch_assoc($result1)) {
                                                $月人数=$row1["yrs"];
                                                $应发工资合计=$row1["sfgzhj"];
                                                
                                            }
                                            ?>
                                            <td><?=$月人数?>人</td>
                                            <td><?=$应发工资合计?>元</td>
                                            <td>15%</td>
                                            <?
                                            $预算成本费用=0;
                                            $预算直接费=0;
                                            $企业管理费=0;
                                            $经营业务费=0;
                                            $sql1="SELECT * FROM `tuqoa_xmhstjzl` WHERE  `sbrq` like '$合同签订月份%'";
                                            $result1 = mysqli_query($link, $sql1);
                                            while ($row1 = mysqli_fetch_assoc($result1)) {
                                                $预算成本费用=$row1["yszcbfy"];
                                                $预算直接费=$row1["yszjf"];
                                                $企业管理费=$row1["qyglf"];
                                                $经营业务费=$row1["jyywf"];
                                                //$实际总成本费用=$row1["gmxzyp"]+$row1["bgf"]+$row1["zjf"]+$row1["dzyhptx"]+$row1["clf"]+$row1["qtfy"]+$row1["zbfwf"]+$row1["qyglf"]+$row1["jyywf"]+$row1["lr"]+$row1["sj"];
                                            }
                                            $实际成本费用=$应发工资合计+$员工社保等上缴金额合计+$企业管理费+$经营业务费;
                                            $实际直接费=$应发工资合计+$员工社保等上缴金额合计;
                                            ?>
                                            <td>¥<?=$实际成本费用?></td>
                                            <td>65%</td>
                                            <?
                                            $完成产值=0;
                                            $人均产值=0;
                                            $sql1="SELECT IFNULL(SUM(wccz), 0) hj FROM `tuqoa_xmcztjb`  WHERE `sbrq` like '$合同签订月份%'";
                                            $result1 = mysqli_query($link, $sql1);
                                            while ($row1 = mysqli_fetch_assoc($result1)) {
                                                $完成产值=$row1["hj"];
                                            }
                                            if($月人数>0){
                                                $人均产值=number_format($完成产值/$月人数,4);
                                            }
                                            ?>
                                            <td><?=$人均产值?>万</td>
                                        </tr>
                                        <?
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <?php
                // 修复WITH语法兼容性问题 - 使用循环方式生成最近6个月产值数据
                $total_wccz = [];

                // 生成最近6个月的数据
                for ($i = 5; $i >= 0; $i--) {
                    $month = date('Y-m', strtotime("-$i month"));

                    // 查询该月的产值数据
                    $sql = "SELECT IFNULL(SUM(wccz), 0) AS total_wccz
                            FROM tuqoa_xmcztjb
                            WHERE DATE_FORMAT(sbrq, '%Y-%m') = '$month'";

                    $result = mysqli_query($link, $sql);
                    if ($result && $row = mysqli_fetch_assoc($result)) {
                        $total_wccz[] = (float)$row["total_wccz"];
                    } else {
                        // 如果查询失败，添加默认值
                        $total_wccz[] = 0;
                    }
                }
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月产值趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="perCapitaChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">尾款趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="tailPaymentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/chart.js"></script>
    <script>
        // 设置默认日期范围（当前月份）
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            
            //document.getElementById('start-date').value = formatDate(firstDay);
            //document.getElementById('end-date').value = formatDate(lastDay);
            
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;
                
                if (!startDate || !endDate) {
                    alert('请选择完整的日期范围');
                    return;
                }
                
                if (new Date(startDate) > new Date(endDate)) {
                    alert('开始日期不能大于结束日期');
                    return;
                }
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询日期范围:', startDate, '至', endDate);
                // 模拟数据刷新
                //alert('已更新数据，日期范围: ' + startDate + ' 至 ' + endDate);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 初始化图表
        function initCharts() {
            // 合同额与到账额趋势图表
            const contractPaymentCtx = document.getElementById('contractPaymentChart').getContext('2d');
            new Chart(contractPaymentCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($month); ?>,
                    datasets: [{
                        label: '新接合同额',
                        data: <?php echo json_encode($fwfhj); ?>,
                        borderColor: '#1e88e5',
                        tension: 0.1
                    }, {
                        label: '到账额',
                        data: <?php echo json_encode($ysjehj); ?>,
                        borderColor: '#43a047',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });

            // 成本与工资占比趋势图表
            const costSalaryCtx = document.getElementById('costSalaryChart').getContext('2d');
            new Chart(costSalaryCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($month); ?>,
                    datasets: [{
                        label: '实际成本',
                        data: [62, 63, 65, 64, 66, 65],
                        borderColor: '#e53935',
                        tension: 0.1
                    }, {
                        label: '预计成本',
                        data: [14, 15, 15, 14, 15, 14],
                        borderColor: '#ffb300',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '占比（%）'
                            }
                        }
                    }
                }
            });

            // 人均产值趋势图表
            const perCapitaCtx = document.getElementById('perCapitaChart').getContext('2d');
            //const labels = <?php echo json_encode($month); ?>;
            
            
            //alert(chartData);
            new Chart(perCapitaCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($month); ?>,
                    datasets: [{
                        label: '月产值合计',
                        data: <?php echo json_encode($total_wccz); ?>,
                        borderColor: '#8e24aa',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });

            // 尾款趋势图表
            const tailPaymentCtx = document.getElementById('tailPaymentChart').getContext('2d');
            new Chart(tailPaymentCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '尾款',
                        data: [100, 150, 200, 180, 220, 200],
                        borderColor: '#fb8c00',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 