<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目合同汇总分析 - 公司数据总览系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/boxicons@2.0.7/css/boxicons.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles/main.css">
</head>
<?php
include '../config.php';
?>
<body>
    <div class="main-content">
        <div class="container-fluid">
            <h2 class="mb-4">项目合同汇总分析</h2>
            
            <!-- 项目选择器 -->
            <form method="post" action="">
            <div class="filter-row mb-4">
                <div class="filter-item">
                    <label for="project-select">选择项目：</label>
                    <select id="project-select" class="form-select" style="width: auto; display: inline-block;" name="gcid">
                        <?
                        $gcid = isset($_POST['gcid']) ? $_POST['gcid'] : '';
                        $sql="SELECT * FROM `tuqoa_gcproject` WHERE `xmzt` not in ('完工项目','完工已结算','合同终止') order by id desc";
                        $result = mysqli_query($link, $sql);
                        while ($row = mysqli_fetch_assoc($result)) {
                            $selected = ($gcid == $row["id"]) ? 'selected' : '';
                        ?>
                        <option value="<?=$row["id"]?>" <?=$selected?>><?=$row["gcname"]?></option>
                        <?
                        }
                        ?>
                    </select>
                </div>
                <button type="submit" id="query-btn" class="btn btn-primary">提交</button>
                
            </div>
            </form>
            
            <div class="row">
                <?
                if($gcid==""){
                    $sqlgcid="SELECT id FROM `tuqoa_gcproject` order by id DESC LIMIT 1";
                    $result5 = mysqli_query($link, $sqlgcid);
                        while ($row5 = mysqli_fetch_assoc($result5)) {
                            $gcid=$row5["id"];
                        }
                }
                $合同总额=0;
                $sql="SELECT ifnull(sum(fwf),0) hj FROM `tuqoa_htgl` WHERE `projectid` =$gcid";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $合同总额=$row["hj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">合同总额</h5>
                            <h2 class="card-text"><?=$合同总额?>万</h2>
                        </div>
                    </div>
                </div>
                <?
                $已收金额=0;
                $sql="SELECT ifnull(sum(ysje),0) hj FROM `tuqoa_htsf` WHERE `projectid`=$gcid";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $已收金额=$row["hj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">已收款总额</h5>
                            <h2 class="card-text">¥<?=$已收金额?>万</h2>
                        </div>
                    </div>
                </div>
                <?
                $wcbfb=0;
                $sql="SELECT wcbfb FROM `tuqoa_gcproject` where id=$gcid";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $wcbfb=$row["wcbfb"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">项目进度</h5>
                            <h2 class="card-text"><?=$wcbfb?>%</h2>
                        </div>
                    </div>
                </div>
                <?
                $项目总产值=0;
                $sql="SELECT ifnull(sum(wccz),0) hj FROM `tuqoa_xmcztjb` WHERE `projectid`=$gcid";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $项目总产值=$row["hj"];
                }
                ?>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">项目总产值</h5>
                            <h2 class="card-text">¥<?=$项目总产值?>万</h2>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <?
                $收款日期=[];
                $预计收款金额=[];
                $已收金额=[];
                $月份=
                $sql="SELECT * FROM `tuqoa_htsf` WHERE `status`=1 and `projectid`=$gcid order by yjsj";
                //print_r($sql);
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $收款日期[]=date('m', strtotime($row["yjsj"]));
                    //$月份 = 
                    $预计收款金额[]=$row["yjje"];
                    $已收金额[]=$row["ysje"];
                }
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">收款与计划对比</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyPaymentChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <?
                $月份=[];
                $应发工资合计=[];
                $每月人数=[];
                $sql="SELECT `gcname` FROM `tuqoa_gcproject` WHERE `id`=$gcid";
                $result = mysqli_query($link, $sql);
                while ($row = mysqli_fetch_assoc($result)) {
                    $gcname=$row['gcname'];
                }
                // 修复WITH语法兼容性问题 - 使用循环方式生成最近6个月工资数据
                $月份 = [];
                $应发工资合计 = [];
                $每月人数 = [];

                // 生成最近6个月的数据
                for ($i = 5; $i >= 0; $i--) {
                    $month = date('Y-m', strtotime("-$i month"));
                    $monthDisplay = date('m', strtotime("-$i month")) . "月";

                    // 查询该月的工资数据
                    $sql = "SELECT
                                COALESCE(SUM(yfgz), 0) AS total_yfgz,
                                COUNT(*) AS record_count
                            FROM `tuqoa_hrsalary`
                            WHERE `xmb` = '$gcname' AND `month` = '$month'";

                    $result = mysqli_query($link, $sql);
                    if ($result && $row = mysqli_fetch_assoc($result)) {
                        $月份[] = $monthDisplay;
                        $应发工资合计[] = (float)$row["total_yfgz"];
                        $每月人数[] = (int)$row["record_count"];
                    } else {
                        // 如果查询失败，添加默认值
                        $月份[] = $monthDisplay;
                        $应发工资合计[] = 0;
                        $每月人数[] = 0;
                    }
                }
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">工资与成本分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyCostChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目产值统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>月份</th>
                                            <th>计划进度</th>
                                            <th>实际进度</th>
                                            <th>进度偏差原因</th>
                                            <th>完成率</th>
                                            <th>本月完成产值（万元）</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?
                                        $进度合计=[];
                                        $进度日期合计=[];
                                        $sql="SELECT *  FROM `tuqoa_xmcztjb` where projectid=".$gcid." order by sbrq desc";
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            $进度合计[]=$row["wcl"];
                                            //$进度日期合计[]=substr($row["sbrq"], -2) . "月";
                                            $进度日期合计[] = substr($row["sbrq"], 5, 2) . "月";
                                        ?>
                                        <tr>
                                            <td><?=$row["sbrq"]?></td>
                                            <td><?=$row["jhjd"]?></td>
                                            <td><?=$row["sjjd"]?></td>
                                            <td><?=$row["pcyy"]?></td>
                                            <td class="text-success"><?=$row["wcl"]?>%</td>
                                            <td>¥<?=$row["wccz"]?>万</td>
                                        </tr>
                                        <?}?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <?
                $进度合计=array_reverse($进度合计);
                $进度日期合计 = array_reverse($进度日期合计);
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度进度完成情况</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyProgressChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">成本构成分析</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="costStructureChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目人员工资明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>上缴时间</th>
                                            <th>上缴人数</th>
                                            <th>应发工资合计</th>
                                            <th>扣款小计</th>
                                            <th>实发工资小计</th>
                                            <th>餐补贴</th>
                                            <th>采暖费</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?
                                        $sql="SELECT 
                                                DATE_FORMAT(STR_TO_DATE(month, '%Y-%m'), '%Y-%m') AS month,
                                                COUNT(*) AS record_count,
                                                ROUND(SUM(yfgz), 2) AS yfgz_total,
                                                ROUND(SUM(kkxj), 2) AS kkxj_total,
                                                ROUND(SUM(sfgz), 2) AS sfgz_total,
                                                ROUND(SUM(cnf), 2) AS cnf_total,
                                                ROUND(SUM(foodbt), 2) AS foodbt_total
                                            FROM 
                                                `tuqoa_hrsalary`
                                            WHERE 
                                                `xmb` = '$gcname'
                                            GROUP BY 
                                                DATE_FORMAT(STR_TO_DATE(month, '%Y-%m'), '%Y-%m')
                                            ORDER BY 
                                                month DESC";
                                        $应发总计=0;
                                        $扣款总计=0;
                                        $实发工资总计=0;
                                        $餐补贴总计=0;
                                        $采暖费总计=0;
                                        //$合计总额=0;
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            $应发总计+=$row["yfgz_total"];
                                            $扣款总计+=$row["kkxj_total"];
                                            $实发工资总计+=$row["sfgz_total"];
                                            $餐补贴总计+=$row["foodbt_total"];
                                            $采暖费总计+=$row["cnf_total"];
                                            
                                        ?>
                                        <tr>
                                            <td><?=$row["month"]?></td>
                                            <td><?=$row["record_count"]?></td>
                                            <td><?=$row["yfgz_total"]?></td>
                                            <td><?=$row["kkxj_total"]?></td>
                                            <td><?=$row["sfgz_total"]?></td>
                                            <td><?=$row["foodbt_total"]?></td>
                                            <td><?=$row["cnf_total"]?></td>
                                            
                                        </tr>
                                        <?}?>
                                        <tr>
                                            <td>合计：</td>
                                            <td>--</td>
                                            <td><?=$应发总计?></td>
                                            <td><?=$扣款总计?></td>
                                            <td><?=$实发工资总计?></td>
                                            <td><?=$餐补贴总计?></td>
                                            <td><?=$采暖费总计?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">项目上缴社保费用明细</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>上缴时间</th>
                                            <th>上缴人数</th>
                                            <th>养老小计</th>
                                            <th>医疗保险小计</th>
                                            <th>失业保险小计</th>
                                            <th>上缴金额</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?
                                        $sql="SELECT 
                                                DATE_FORMAT(ys, '%Y-%m') AS month,
                                                COUNT(*) AS record_count,
                                                ROUND(SUM(ylxj), 2) AS ylxj_total,
                                                ROUND(SUM(yiliaobxxj), 2) AS yiliaobxxj_total,
                                                ROUND(SUM(sybxxj), 2) AS sybxxj_total,
                                                ROUND(SUM(sjje), 2) AS sjje_total
                                            FROM 
                                                `tuqoa_xmsjbxmx`
                                            WHERE 
                                                `projectid` = $gcid
                                            GROUP BY 
                                                DATE_FORMAT(ys, '%Y-%m')
                                            ORDER BY 
                                                month DESC";
                                        $养老总计=0;
                                        $医疗保险总计=0;
                                        $失业保险总计=0;
                                        $合计总额=0;
                                        $result = mysqli_query($link, $sql);
                                        while ($row = mysqli_fetch_assoc($result)) {
                                            $养老总计+=$row["ylxj_total"];
                                            $医疗保险总计+=$row["yiliaobxxj_total"];
                                            $失业保险总计+=$row["sybxxj_total"];
                                            $合计总额+=$row["sjje_total"];
                                        ?>
                                        <tr>
                                            <td><?=$row["month"]?></td>
                                            <td><?=$row["record_count"]?></td>
                                            <td><?=$row["ylxj_total"]?>元</td>
                                            <td><?=$row["yiliaobxxj_total"]?>元</td>
                                            <td><?=$row["sybxxj_total"]?>元</td>
                                            <td><?=$row["sjje_total"]?>元</td>
                                        </tr>
                                        <?}?>
                                        <tr>
                                            <td>合计：</td>
                                            <td>--</td>
                                            <td><?=$养老总计?></td>
                                            <td><?=$医疗保险总计?></td>
                                            <td><?=$失业保险总计?></td>
                                            <td><?=$合计总额?></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">月度人员变化趋势</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyStaffChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <?
                
                ?>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">产值与工资对比</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="perCapitaChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // 设置默认项目
        document.addEventListener('DOMContentLoaded', function() {
            // 查询按钮点击事件
            document.getElementById('query-btn').addEventListener('click', function() {
                const selectedProject = document.getElementById('project-select').value;
                
                // 这里可以添加查询逻辑，例如AJAX请求获取数据
                console.log('查询项目:', selectedProject);
                // 模拟数据刷新
            //    alert('已更新数据，项目: ' + selectedProject);
            });
            
            // 初始化图表
            initCharts();
        });
        
        // 初始化图表
        function initCharts() {
            // 月度收款与计划对比图表
            const monthlyPaymentCtx = document.getElementById('monthlyPaymentChart').getContext('2d');
            new Chart(monthlyPaymentCtx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($收款日期); ?>,
                    datasets: [
                        {
                            label: '计划收款',
                            data: <?php echo json_encode($预计收款金额); ?>,
                            backgroundColor: '#1e88e5'
                        },
                        {
                            label: '实际收款',
                            data: <?php echo json_encode($已收金额); ?>,
                            backgroundColor: '#43a047'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });

            // 月度人员与成本分析图表
            const monthlyCostCtx = document.getElementById('monthlyCostChart').getContext('2d');
            new Chart(monthlyCostCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($月份); ?>,
                    datasets: [
                        {
                            label: '月度人数',
                            data: <?php echo json_encode($每月人数); ?>,
                            borderColor: '#1e88e5',
                            backgroundColor: 'rgba(30, 136, 229, 0.1)',
                            yAxisID: 'y',
                            tension: 0.3
                        },
                        {
                            label: '应发工资合计',
                            data: <?php echo json_encode($应发工资合计); ?>,
                            borderColor: '#e53935',
                            backgroundColor: 'rgba(229, 57, 53, 0.1)',
                            yAxisID: 'y1',
                            tension: 0.3
                        },
                        {
                            label: '成本占比',
                            data: [50, 60, 66.7, 88, 71.4, 93.3, 150],
                            borderColor: '#ffb300',
                            backgroundColor: 'rgba(255, 179, 0, 0.1)',
                            yAxisID: 'y1',
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '人数'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '占比（%）'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });

            // 月度进度完成情况图表
            const monthlyProgressCtx = document.getElementById('monthlyProgressChart').getContext('2d');
            new Chart(monthlyProgressCtx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($进度日期合计); ?>,
                    datasets: [
                        {
                            label: '计划进度',
                            data: <?php echo json_encode($进度合计); ?>,
                            borderColor: '#1e88e5',
                            backgroundColor: 'rgba(30, 136, 229, 0.1)',
                            tension: 0.3
                        },
                        {
                            label: '实际进度',
                            data: [18, 32, 48, 55, 75, 85, 0],
                            borderColor: '#43a047',
                            backgroundColor: 'rgba(67, 160, 71, 0.1)',
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: '进度（%）'
                            }
                        }
                    }
                }
            });

            // 成本构成分析图表
            const costStructureCtx = document.getElementById('costStructureChart').getContext('2d');
            new Chart(costStructureCtx, {
                type: 'pie',
                data: {
                    labels: ['人工成本', '材料成本', '设备成本', '分包成本', '其他成本'],
                    datasets: [{
                        data: [32, 37.5, 16.3, 10, 4.2],
                        backgroundColor: ['#1e88e5', '#e53935', '#43a047', '#ffb300', '#8e24aa']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 月度人员变化趋势图表
            const monthlyStaffCtx = document.getElementById('monthlyStaffChart').getContext('2d');
            new Chart(monthlyStaffCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
                    datasets: [
                        {
                            label: '计划人数',
                            data: [12, 15, 18, 20, 22, 25, 28],
                            borderColor: '#1e88e5',
                            backgroundColor: 'rgba(30, 136, 229, 0.1)',
                            tension: 0.3
                        },
                        {
                            label: '实际人数',
                            data: [15, 18, 20, 22, 25, 28, 30],
                            borderColor: '#43a047',
                            backgroundColor: 'rgba(67, 160, 71, 0.1)',
                            tension: 0.3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '人数'
                            }
                        }
                    }
                }
            });

            // 人均产值与工资对比图表
            const perCapitaCtx = document.getElementById('perCapitaChart').getContext('2d');
            new Chart(perCapitaCtx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [
                        {
                            label: '人均产值',
                            data: [8, 6.7, 6, 4.5, 5.6, 4.3],
                            backgroundColor: '#1e88e5'
                        },
                        {
                            label: '人均工资',
                            data: [2, 2, 2, 2, 2, 2],
                            backgroundColor: '#e53935'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额（万元）'
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html> 